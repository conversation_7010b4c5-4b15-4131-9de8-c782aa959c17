import * as cdk from 'aws-cdk-lib';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as cognitoIdentity from 'aws-cdk-lib/aws-cognito';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as route53_targets from 'aws-cdk-lib/aws-route53-targets';
import * as CustomResources from 'aws-cdk-lib/custom-resources';
import { Construct } from 'constructs';
import * as path from 'path';
import { VPCStack } from '../vpc-stack';


export class CognitoStack extends cdk.Stack {
    public readonly userPool: cognito.UserPool;
    public readonly userPoolDomain: cognito.UserPoolDomain;
    public readonly memberClient: cognito.UserPoolClient;
    public readonly internalClient: cognito.UserPoolClient;
    constructor(scope: Construct, id: string, cert: acm.ICertificate, vpcStack: VPCStack, props?: cdk.StackProps) {
        super(scope, id, props);

        // Create a User Pool
        const userPool = new cognito.UserPool(this, 'UserPool', {
            userPoolName: 'MyUserPool',
            selfSignUpEnabled: false, // Disable "Create an account" on Hosted UI
            signInAliases: { email: true },
            signInCaseSensitive: false,
            // autoVerify: { email: true }, // this must be set below :(
            // see https://github.com/aws/aws-cdk/issues/10002#issuecomment-**********
            
            // Custom attributes are currently not used or needed, but just adding for absolute reflection of userpool.
            customAttributes: {
                'provisioned': new cognito.StringAttribute({ mutable: true })
            },
        });
        this.userPool = userPool;

        const membersGroup = new cognito.CfnUserPoolGroup(this, 'MembersGroup', {
            userPoolId: userPool.userPoolId,
            groupName: 'members',
            description: 'Group for members',
        });

        const internalGroup = new cognito.CfnUserPoolGroup(this, 'InternalGroup', {
            userPoolId: userPool.userPoolId,
            groupName: 'internal',
            description: 'Group for internal employees',
        });

        // Create a User Pool Client
        const memberClient = new cognito.UserPoolClient(this, 'MemberClient', {
            userPool,
            generateSecret: false,
            authFlows: {
                user: true
            }
        });
        this.memberClient = memberClient;

        const oktaProdMetadataUrl = "https://gethero.okta.com/app/exk1wx7pr3rQqDomN1d8/sso/saml/metadata";
        const oktaSandboxMetadataUrl = "https://gethero.okta.com/app/exk1wzdmtadxej3up1d8/sso/saml/metadata";

        const oktaProviderProd = new cognito.UserPoolIdentityProviderSaml(this, 'OktaProdProvider', {
            userPool: this.userPool,
            name: 'OktaProd',
            metadata: cognito.UserPoolIdentityProviderSamlMetadata.url(oktaProdMetadataUrl),
            // encryptedResponses: true,
            // requestSigningAlgorithm: cognito.SigningAlgorithm.RSA_SHA256,
            // idpInitiated: true,
            attributeMapping: {
                email: cognito.ProviderAttribute.other('email'),
                familyName: cognito.ProviderAttribute.other('family_name'),
                givenName: cognito.ProviderAttribute.other('given_name'),
            },
        });

        const oktaProviderSandbox = new cognito.UserPoolIdentityProviderSaml(this, 'OktaSandboxProvider', {
            userPool: this.userPool,
            name: 'OktaSandbox',
            metadata: cognito.UserPoolIdentityProviderSamlMetadata.url(oktaSandboxMetadataUrl),
            // encryptedResponses: true,
            // requestSigningAlgorithm: cognito.SigningAlgorithm.RSA_SHA256,
            // idpInitiated: true,
            attributeMapping: {
                email: cognito.ProviderAttribute.other('email'),
                familyName: cognito.ProviderAttribute.other('family_name'),
                givenName: cognito.ProviderAttribute.other('given_name'),
            },
        });

        // Create a User Pool Client
        const internalClient = new cognito.UserPoolClient(this, 'InternalClient', {
            userPool,
            generateSecret: false,
            enableTokenRevocation: true,
            authFlows: {
                // user: true,
                userPassword: true,
                userSrp: true
            },
            supportedIdentityProviders: [
                cognito.UserPoolClientIdentityProvider.COGNITO,
                cognito.UserPoolClientIdentityProvider.custom('OktaProd'),
                cognito.UserPoolClientIdentityProvider.custom('OktaSandbox')
            ],
            oAuth: {
                flows: {
                    authorizationCodeGrant: true
                },
                callbackUrls: [
                    'https://command.gethero.com',
                    'myapp://callback/',

                ],
                logoutUrls: [
                    'https://command.gethero.com',
                    'myapp://callback/'
                ],
            },
        });
        this.internalClient = internalClient;
        internalClient.node.addDependency(oktaProviderProd);
        internalClient.node.addDependency(oktaProviderSandbox);

        const postConfirmationLambda = new lambda.Function(this, 'PostConfirmationLambda', {
            functionName: 'PostConfirmationLambda',
            runtime: lambda.Runtime.PROVIDED_AL2023,
            handler: 'main',
            timeout: cdk.Duration.seconds(10),
            vpc: vpcStack.vpc,
            code: lambda.Code.fromAsset(path.join(__dirname, '../cognito-stack/postConfirmationLambda/bootstrap.zip')), // Path to Go Lambda zip 
            environment: {
                USER_POOL_ID: userPool.userPoolId,
                MEMBER_CLIENT_ID: memberClient.userPoolClientId,
                INTERNAL_CLIENT_ID: internalClient.userPoolClientId,
                MEMBER_ROLE_NAME: 'Member',
                DEFAULT_ROLE_NAME: 'Responder',
                WORKFLOW_SERVICE_URL: 'https://workflow.lb.api.gethero.com',
                ORGS_SERVICE_URL: 'https://orgs.lb.api.gethero.com',
                PERMS_SERVICE_URL: 'https://perms.lb.api.gethero.com',
                OKTA_ORG_MAPPING: (() => {
                    const PROD_ISSUER = 'http://www.okta.com/exk1wx7pr3rQqDomN1d8';
                    const SANDBOX_ISSUER = 'http://www.okta.com/exk1wzdmtadxej3up1d8';
                    return JSON.stringify({
                        [PROD_ISSUER]: '1',
                        [SANDBOX_ISSUER]: '2'
                    });
                })(),
            }
        });

        // Give Cognito permission to invoke the Lambda
        postConfirmationLambda.addPermission('AllowCognitoInvokePost', {
            principal: new iam.ServicePrincipal('cognito-idp.amazonaws.com'),
            action: 'lambda:InvokeFunction',
            sourceArn: userPool.userPoolArn,
        });

        const preSignUpLambda = new lambda.Function(this, 'PreSignUpLambda', {
            functionName: 'PreSignUpLambda',
            runtime: lambda.Runtime.PROVIDED_AL2023,
            handler: 'main',
            timeout: cdk.Duration.seconds(10),
            vpc: vpcStack.vpc,
            code: lambda.Code.fromAsset(path.join(__dirname, '../cognito-stack/preSignUpLambda/bootstrap.zip')), // Path to Go Lambda zip 
            environment: {
                ORGS_SERVICE_URL: 'https://orgs.lb.api.gethero.com'
            }
        });

        // Give Cognito permission to invoke the Lambda
        preSignUpLambda.addPermission('AllowCognitoInvokePre', {
            principal: new iam.ServicePrincipal('cognito-idp.amazonaws.com'),
            action: 'lambda:InvokeFunction',
            sourceArn: userPool.userPoolArn,
        });

        // HACK - comment out this custom resource, deploy the userpool and lambda, uncomment, deploy again
        // fix for circular dependency when trying to do userPool.addTrigger(cognito.UserPoolOperation.POST_CONFIRMATION, postConfirmationLambda);
        // found here - https://github.com/aws/aws-cdk/issues/10002#issuecomment-854169838
        new CustomResources.AwsCustomResource(this, "UpdateUserPool", {
            resourceType: "Custom::UpdateUserPool",
            // see https://docs.aws.amazon.com/cognito-user-identity-pools/latest/APIReference/API_UpdateUserPool.html for parameters
            onCreate: {
                region: this.region,
                service: "CognitoIdentityServiceProvider",
                action: "updateUserPool",
                parameters: {
                    UserPoolId: userPool.userPoolId,
                    AutoVerifiedAttributes: ['email'],
                    LambdaConfig: {
                        PostConfirmation: postConfirmationLambda.functionArn,
                        PreSignUp: preSignUpLambda.functionArn,
                        // Uncomment to override the default triggers
                        //   DefineAuthChallenge: defineAuthChallengeHandler.functionArn,
                        //   CreateAuthChallenge: createAuthChallengeHandler.functionArn,
                        //   VerifyAuthChallengeResponse: verifyAuthChallengeResponseHandler.functionArn,
                    },
                },
                physicalResourceId: CustomResources.PhysicalResourceId.of(userPool.userPoolId),
            },
            onUpdate: {
                region: this.region,
                service: "CognitoIdentityServiceProvider",
                action: "updateUserPool",
                parameters: {
                    UserPoolId: userPool.userPoolId,
                    AutoVerifiedAttributes: ['email'],
                    LambdaConfig: {
                        PostConfirmation: postConfirmationLambda.functionArn,
                        PreSignUp: preSignUpLambda.functionArn,
                    },
                },
                physicalResourceId: CustomResources.PhysicalResourceId.of(userPool.userPoolId),
            },
            policy: CustomResources.AwsCustomResourcePolicy.fromSdkCalls({ resources: CustomResources.AwsCustomResourcePolicy.ANY_RESOURCE }),
        });

        // Grant Lambda permissions to manage Cognito groups
        postConfirmationLambda.addToRolePolicy(new iam.PolicyStatement({
            actions: ['cognito-idp:AdminAddUserToGroup'],
            resources: [userPool.userPoolArn]
        }));

        const authDomainName = 'auth.gethero.com';



        // CUSTOM DOMAIN
        // Create a Certificate for domainName in us-east-1
        // const certificate = new acm.Certificate(this, 'Certificate', {
        //     domainName: authDomainName,
        //     validation: acm.CertificateValidation.fromDns(),
        // });
        // Add a domain to the User Pool
        const userPoolDomain = new cognito.UserPoolDomain(this, 'UserPoolDomain', {
            userPool,
            customDomain: {
                domainName: authDomainName,
                certificate: cert
            }
        });
        this.userPoolDomain = userPoolDomain;

        const hostedZone = route53.HostedZone.fromHostedZoneAttributes(this, id + 'hostedZone', {
            hostedZoneId: 'Z08666901121Y80T7UGG9',// The gethero.com hosted zone ID, found in the Route 53 console
            zoneName: 'gethero.com', // The domain name of the hosted zone
        });

        new route53.ARecord(this, 'UserPoolCloudFrontAliasRecord', {
            zone: hostedZone,
            recordName: authDomainName,
            target: route53.RecordTarget.fromAlias(new route53_targets.UserPoolDomainTarget(userPoolDomain)),
        });
        // CUSTOM DOMAIN



        // Create an Identity Pool
        const memberIdentityPool = new cognitoIdentity.CfnIdentityPool(this, 'MemberIdentityPool', {
            allowUnauthenticatedIdentities: false,
            cognitoIdentityProviders: [
                {
                    clientId: memberClient.userPoolClientId,
                    providerName: userPool.userPoolProviderName,
                },
            ],
        });

        const internalIdentityPool = new cognitoIdentity.CfnIdentityPool(this, 'InternalIdentityPool', {
            allowUnauthenticatedIdentities: false,
            cognitoIdentityProviders: [
                {
                    clientId: internalClient.userPoolClientId,
                    providerName: userPool.userPoolProviderName,
                },
            ],
        });

        // Authenticated Role
        const memberAuthenticatedRole = new iam.Role(this, 'MemberAuthenticatedRole', {
            assumedBy: new iam.FederatedPrincipal(
                'cognito-identity.amazonaws.com',
                {
                    'StringEquals': { 'cognito-identity.amazonaws.com:aud': memberIdentityPool.ref },
                    'ForAnyValue:StringLike': { 'cognito-identity.amazonaws.com:amr': 'authenticated' },
                },
                'sts:AssumeRoleWithWebIdentity'
            ),
            managedPolicies: [
                // iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
            ],
        });

        // Add KVS permissions to member role
        memberAuthenticatedRole.addToPolicy(new iam.PolicyStatement({
            actions: [
                'kinesisvideo:DescribeSignalingChannel',
                'kinesisvideo:GetSignalingChannelEndpoint',
                'kinesisvideo:GetIceServerConfig',
                'kinesisvideo:ConnectAsViewer',
                'kinesisvideo:JoinStorageSession',
                'kinesisvideo:GetDataEndpoint'
            ],
            resources: ['*']
        }));

        // Authenticated Role
        const internalAuthenticatedRole = new iam.Role(this, 'InternalAuthenticatedRole', {
            assumedBy: new iam.FederatedPrincipal(
                'cognito-identity.amazonaws.com',
                {
                    'StringEquals': { 'cognito-identity.amazonaws.com:aud': internalIdentityPool.ref },
                    'ForAnyValue:StringLike': { 'cognito-identity.amazonaws.com:amr': 'authenticated' },
                },
                'sts:AssumeRoleWithWebIdentity'
            ),
            managedPolicies: [
                // iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
            ],
        });

        // Add KVS permissions to internal role
        internalAuthenticatedRole.addToPolicy(new iam.PolicyStatement({
            actions: [
                'kinesisvideo:DescribeSignalingChannel',
                'kinesisvideo:GetSignalingChannelEndpoint',
                'kinesisvideo:GetIceServerConfig',
                'kinesisvideo:ConnectAsViewer',
                'kinesisvideo:JoinStorageSession',
                'kinesisvideo:GetDataEndpoint'
            ],
            resources: ['*']
        }));

        // Attach roles to the Identity Pool
        new cognitoIdentity.CfnIdentityPoolRoleAttachment(this, 'MemberIdentityPoolRoleAttachment', {
            identityPoolId: memberIdentityPool.ref,
            roles: {
                authenticated: memberAuthenticatedRole.roleArn,
            },
        });

        new cognitoIdentity.CfnIdentityPoolRoleAttachment(this, 'InternalIdentityPoolRoleAttachment', {
            identityPoolId: internalIdentityPool.ref,
            roles: {
                authenticated: internalAuthenticatedRole.roleArn,
            },
        });
    }
}