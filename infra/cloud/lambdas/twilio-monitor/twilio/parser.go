package twilio

import (
	"fmt"
	"net/url"
	"strconv"
	"time"
)

// TwilioError represents a parsed Twilio error event
type TwilioError struct {
	// Core identifiers
	CallSid     string `json:"call_sid"`
	AccountSid  string `json:"account_sid"`
	
	// Error details
	ErrorCode    int    `json:"error_code"`
	ErrorMessage string `json:"error_message"`
	
	// Call context
	From      string `json:"from"`
	To        string `json:"to"`
	Direction string `json:"direction"`
	Status    string `json:"status"`
	
	// Timing
	Timestamp time.Time `json:"timestamp"`
	Duration  int       `json:"duration,omitempty"`
	
	// Additional context
	URL        string            `json:"url,omitempty"`
	RawPayload map[string]string `json:"raw_payload"`
}

// Severity returns the error severity based on error code
func (te *TwilioError) Severity() string {
	switch {
	case te.ErrorCode >= 30000 && te.ErrorCode < 40000:
		return "critical" // Call setup failures
	case te.ErrorCode >= 40000 && te.ErrorCode < 50000:
		return "high"     // Authentication/authorization errors
	case te.ErrorCode >= 50000 && te.ErrorCode < 60000:
		return "medium"   // Media/network issues
	default:
		return "low"      // Other errors
	}
}

// Category returns the error category for routing
func (te *TwilioError) Category() string {
	switch {
	case te.ErrorCode >= 30000 && te.ErrorCode < 32000:
		return "call_setup"
	case te.ErrorCode >= 32000 && te.ErrorCode < 34000:
		return "media"
	case te.ErrorCode >= 40000 && te.ErrorCode < 42000:
		return "authentication"
	case te.ErrorCode >= 50000 && te.ErrorCode < 52000:
		return "network"
	default:
		return "other"
	}
}

// ParseTwilioWebhook parses Twilio webhook form data into TwilioError
func ParseTwilioWebhook(formData url.Values) (*TwilioError, error) {
	errorCode, err := strconv.Atoi(formData.Get("ErrorCode"))
	if err != nil {
		return nil, fmt.Errorf("invalid error code: %w", err)
	}
	
	// Parse duration if present
	var duration int
	if durationStr := formData.Get("CallDuration"); durationStr != "" {
		duration, _ = strconv.Atoi(durationStr)
	}
	
	// Create raw payload map for debugging
	rawPayload := make(map[string]string)
	for key, values := range formData {
		if len(values) > 0 {
			rawPayload[key] = values[0]
		}
	}
	
	twilioError := &TwilioError{
		CallSid:      formData.Get("CallSid"),
		AccountSid:   formData.Get("AccountSid"),
		ErrorCode:    errorCode,
		ErrorMessage: formData.Get("ErrorMessage"),
		From:         formData.Get("From"),
		To:           formData.Get("To"),
		Direction:    formData.Get("Direction"),
		Status:       formData.Get("CallStatus"),
		Timestamp:    time.Now(), // Twilio doesn't always provide timestamp
		Duration:     duration,
		URL:          formData.Get("Url"),
		RawPayload:   rawPayload,
	}
	
	return twilioError, nil
}