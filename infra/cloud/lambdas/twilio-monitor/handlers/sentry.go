package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"
	
	"twilio-monitor/twilio"
)

// SentryHandler sends alerts to Sentry
type Sentry<PERSON>andler struct {
	dsn    string
	client *http.Client
}

// SentryEvent represents a Sentry event payload
type SentryEvent struct {
	EventID     string                 `json:"event_id"`
	Timestamp   string                 `json:"timestamp"`
	Level       string                 `json:"level"`
	Logger      string                 `json:"logger"`
	Platform    string                 `json:"platform"`
	Message     SentryMessage          `json:"message"`
	Tags        map[string]string      `json:"tags"`
	Extra       map[string]interface{} `json:"extra"`
	Fingerprint []string               `json:"fingerprint"`
}

type SentryMessage struct {
	Formatted string `json:"formatted"`
}

// NewSentryHandler creates a new Sentry alert handler
func NewSentryHandler() *SentryHandler {
	return &SentryHandler{
		dsn: os.Getenv("SENTRY_DSN"),
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// SendAlert sends an alert to Sentry
func (h *SentryHandler) SendAlert(ctx context.Context, errorData *twilio.TwilioError) error {
	if h.dsn == "" {
		return fmt.Errorf("SENTRY_DSN environment variable not set")
	}
	
	// Parse DSN to get project info
	parsedDSN, err := url.Parse(h.dsn)
	if err != nil {
		return fmt.Errorf("invalid Sentry DSN: %w", err)
	}
	
	// Extract project ID from path
	pathParts := strings.Split(strings.Trim(parsedDSN.Path, "/"), "/")
	if len(pathParts) < 1 {
		return fmt.Errorf("invalid Sentry DSN format")
	}
	projectID := pathParts[len(pathParts)-1]
	
	// Build Sentry API URL
	sentryURL := fmt.Sprintf("%s://%s/api/%s/store/", parsedDSN.Scheme, parsedDSN.Host, projectID)
	
	event := SentryEvent{
		EventID:   generateEventID(),
		Timestamp: errorData.Timestamp.Format(time.RFC3339),
		Level:     mapSeverityToSentry(errorData.Severity()),
		Logger:    "twilio-monitor",
		Platform:  "go",
		Message: SentryMessage{
			Formatted: fmt.Sprintf("Twilio Error %d: %s", errorData.ErrorCode, errorData.ErrorMessage),
		},
		Tags: map[string]string{
			"error_code":  fmt.Sprintf("%d", errorData.ErrorCode),
			"call_sid":    errorData.CallSid,
			"direction":   errorData.Direction,
			"category":    errorData.Category(),
			"severity":    errorData.Severity(),
		},
		Extra: map[string]interface{}{
			"account_sid": errorData.AccountSid,
			"from":        errorData.From,
			"to":          errorData.To,
			"status":      errorData.Status,
			"duration":    errorData.Duration,
			"url":         errorData.URL,
			"raw_payload": errorData.RawPayload,
		},
		Fingerprint: []string{
			"twilio-error",
			fmt.Sprintf("%d", errorData.ErrorCode),
			errorData.Category(),
		},
	}
	
	jsonData, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal Sentry event: %w", err)
	}
	
	req, err := http.NewRequestWithContext(ctx, "POST", sentryURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	
	// Extract auth key from DSN
	if parsedDSN.User != nil {
		authKey := parsedDSN.User.Username()
		req.Header.Set("X-Sentry-Auth", fmt.Sprintf("Sentry sentry_version=7,sentry_key=%s", authKey))
	}
	
	req.Header.Set("Content-Type", "application/json")
	
	resp, err := h.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request to Sentry: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Sentry API returned status %d", resp.StatusCode)
	}
	
	return nil
}

// mapSeverityToSentry maps internal severity to Sentry severity levels
func mapSeverityToSentry(severity string) string {
	switch severity {
	case "critical":
		return "fatal"
	case "high":
		return "error"
	case "medium":
		return "warning"
	case "low":
		return "info"
	default:
		return "info"
	}
}

// generateEventID generates a simple event ID for Sentry
func generateEventID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}