package handlers

import (
	"context"
	"fmt"
	"os"
	
	"twilio-monitor/twilio"
)

// AlertHandler defines the interface for sending alerts
type AlertHandler interface {
	SendAlert(ctx context.Context, errorData *twilio.TwilioError) error
}

// GetAlertHandler returns the appropriate alert handler based on environment configuration
func GetAlertHandler() (Alert<PERSON>and<PERSON>, error) {
	alertType := os.Getenv("ALERT_TYPE")
	
	switch alertType {
	case "pagerduty":
		return NewPagerDutyHandler(), nil
	case "sentry":
		return NewSentryHandler(), nil
	case "slack":
		return NewSlackHandler(), nil
	case "":
		return nil, fmt.Errorf("ALERT_TYPE environment variable not set")
	default:
		return nil, fmt.Errorf("unsupported alert type: %s", alertType)
	}
}