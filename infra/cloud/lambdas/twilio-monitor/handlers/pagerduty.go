package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"
	
	"twilio-monitor/twilio"
)

// PagerDutyHandler sends alerts to PagerDuty using Events API v2
type PagerDutyHandler struct {
	routingKey string
	client     *http.Client
}

// PagerDutyEvent represents a PagerDuty event payload
type PagerDutyEvent struct {
	RoutingKey  string                 `json:"routing_key"`
	EventAction string                 `json:"event_action"`
	DedupKey    string                 `json:"dedup_key"`
	Payload     PagerDutyEventPayload  `json:"payload"`
	Client      string                 `json:"client"`
	ClientURL   string                 `json:"client_url,omitempty"`
}

type PagerDutyEventPayload struct {
	Summary       string                 `json:"summary"`
	Source        string                 `json:"source"`
	Severity      string                 `json:"severity"`
	Timestamp     string                 `json:"timestamp"`
	Component     string                 `json:"component"`
	Group         string                 `json:"group"`
	Class         string                 `json:"class"`
	CustomDetails map[string]interface{} `json:"custom_details"`
}

// NewPagerDutyHandler creates a new PagerDuty alert handler
func NewPagerDutyHandler() *PagerDutyHandler {
	return &PagerDutyHandler{
		routingKey: os.Getenv("PAGERDUTY_ROUTING_KEY"),
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// SendAlert sends an alert to PagerDuty
func (h *PagerDutyHandler) SendAlert(ctx context.Context, errorData *twilio.TwilioError) error {
	if h.routingKey == "" {
		return fmt.Errorf("PAGERDUTY_ROUTING_KEY environment variable not set")
	}
	
	event := PagerDutyEvent{
		RoutingKey:  h.routingKey,
		EventAction: "trigger",
		DedupKey:    fmt.Sprintf("twilio-error-%s-%d", errorData.CallSid, errorData.ErrorCode),
		Client:      "Twilio Monitor Lambda",
		Payload: PagerDutyEventPayload{
			Summary:   fmt.Sprintf("Twilio Error %d: %s", errorData.ErrorCode, errorData.ErrorMessage),
			Source:    "twilio",
			Severity:  mapSeverityToPagerDuty(errorData.Severity()),
			Timestamp: errorData.Timestamp.Format(time.RFC3339),
			Component: "twilio-voice",
			Group:     errorData.Category(),
			Class:     "communication_error",
			CustomDetails: map[string]interface{}{
				"call_sid":     errorData.CallSid,
				"account_sid":  errorData.AccountSid,
				"error_code":   errorData.ErrorCode,
				"from":         errorData.From,
				"to":           errorData.To,
				"direction":    errorData.Direction,
				"status":       errorData.Status,
				"duration":     errorData.Duration,
				"raw_payload":  errorData.RawPayload,
			},
		},
	}
	
	jsonData, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal PagerDuty event: %w", err)
	}
	
	req, err := http.NewRequestWithContext(ctx, "POST", "https://events.pagerduty.com/v2/enqueue", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	
	req.Header.Set("Content-Type", "application/json")
	
	resp, err := h.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request to PagerDuty: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusAccepted {
		return fmt.Errorf("PagerDuty API returned status %d", resp.StatusCode)
	}
	
	return nil
}

// mapSeverityToPagerDuty maps internal severity to PagerDuty severity levels
func mapSeverityToPagerDuty(severity string) string {
	switch severity {
	case "critical":
		return "critical"
	case "high":
		return "error"
	case "medium":
		return "warning"
	case "low":
		return "info"
	default:
		return "info"
	}
}