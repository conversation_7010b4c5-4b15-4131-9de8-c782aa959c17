package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"
	
	"twilio-monitor/twilio"
)

// SlackHandler sends alerts to Slack via webhook
type Slack<PERSON>and<PERSON> struct {
	webhookURL string
	client     *http.Client
}

// SlackMessage represents a Slack webhook payload
type SlackMessage struct {
	Text        string             `json:"text"`
	Username    string             `json:"username,omitempty"`
	IconEmoji   string             `json:"icon_emoji,omitempty"`
	Channel     string             `json:"channel,omitempty"`
	Attachments []SlackAttachment  `json:"attachments,omitempty"`
}

type SlackAttachment struct {
	Color     string       `json:"color"`
	Title     string       `json:"title"`
	Text      string       `json:"text"`
	Fields    []SlackField `json:"fields"`
	Timestamp int64        `json:"ts"`
}

type SlackField struct {
	Title string `json:"title"`
	Value string `json:"value"`
	Short bool   `json:"short"`
}

// NewSlackHandler creates a new Slack alert handler
func NewSlackHandler() *SlackHandler {
	return &SlackHandler{
		webhookURL: os.Getenv("SLACK_WEBHOOK_URL"),
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// SendAlert sends an alert to Slack
func (h *SlackHandler) SendAlert(ctx context.Context, errorData *twilio.TwilioError) error {
	if h.webhookURL == "" {
		return fmt.Errorf("SLACK_WEBHOOK_URL environment variable not set")
	}
	
	color := mapSeverityToSlackColor(errorData.Severity())
	
	message := SlackMessage{
		Username:  "Twilio Monitor",
		IconEmoji: ":telephone_receiver:",
		Text:      fmt.Sprintf("🚨 Twilio Error Alert - Severity: %s", errorData.Severity()),
		Attachments: []SlackAttachment{
			{
				Color:     color,
				Title:     fmt.Sprintf("Error %d: %s", errorData.ErrorCode, errorData.ErrorMessage),
				Text:      fmt.Sprintf("Call SID: `%s`", errorData.CallSid),
				Timestamp: errorData.Timestamp.Unix(),
				Fields: []SlackField{
					{
						Title: "Error Code",
						Value: fmt.Sprintf("%d", errorData.ErrorCode),
						Short: true,
					},
					{
						Title: "Category",
						Value: errorData.Category(),
						Short: true,
					},
					{
						Title: "From",
						Value: errorData.From,
						Short: true,
					},
					{
						Title: "To",
						Value: errorData.To,
						Short: true,
					},
					{
						Title: "Direction",
						Value: errorData.Direction,
						Short: true,
					},
					{
						Title: "Status",
						Value: errorData.Status,
						Short: true,
					},
				},
			},
		},
	}
	
	// Add duration if available
	if errorData.Duration > 0 {
		message.Attachments[0].Fields = append(message.Attachments[0].Fields, SlackField{
			Title: "Duration",
			Value: fmt.Sprintf("%d seconds", errorData.Duration),
			Short: true,
		})
	}
	
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal Slack message: %w", err)
	}
	
	req, err := http.NewRequestWithContext(ctx, "POST", h.webhookURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	
	req.Header.Set("Content-Type", "application/json")
	
	resp, err := h.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request to Slack: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Slack API returned status %d", resp.StatusCode)
	}
	
	return nil
}

// mapSeverityToSlackColor maps internal severity to Slack message colors
func mapSeverityToSlackColor(severity string) string {
	switch severity {
	case "critical":
		return "danger"    // Red
	case "high":
		return "warning"   // Orange/Yellow
	case "medium":
		return "#439FE0"   // Blue
	case "low":
		return "good"      // Green
	default:
		return "#808080"   // Gray
	}
}