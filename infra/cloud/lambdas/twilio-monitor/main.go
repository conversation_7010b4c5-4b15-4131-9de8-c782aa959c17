package main

import (
	"context"
	"fmt"
	"log"
	"net/url"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"

	"twilio-monitor/handlers"
	"twilio-monitor/twilio"
)

func handler(ctx context.Context, event events.LambdaFunctionURLRequest) (events.LambdaFunctionURLResponse, error) {
	log.Printf("Received webhook: Method=%s, Headers=%+v", event.RequestContext.HTTP.Method, event.Headers)
	
	// Only accept POST requests
	if event.RequestContext.HTTP.Method != "POST" {
		return events.LambdaFunctionURLResponse{
			StatusCode: 405,
			Body:       "Method not allowed",
			Headers: map[string]string{
				"Content-Type": "text/plain",
			},
		}, nil
	}
	
	// Parse form data from request body
	formData, err := url.ParseQuery(event.Body)
	if err != nil {
		log.Printf("Error parsing form data: %v", err)
		return events.LambdaFunctionURLResponse{
			StatusCode: 400,
			Body:       "Invalid form data",
			Headers: map[string]string{
				"Content-Type": "text/plain",
			},
		}, nil
	}
	
	// Validate that this looks like a Twilio error webhook
	if !isValidTwilioErrorWebhook(formData) {
		log.Printf("Invalid Twilio webhook data: %+v", formData)
		return events.LambdaFunctionURLResponse{
			StatusCode: 400,
			Body:       "Invalid Twilio webhook data",
			Headers: map[string]string{
				"Content-Type": "text/plain",
			},
		}, nil
	}
	
	// Parse Twilio error data
	twilioError, err := twilio.ParseTwilioWebhook(formData)
	if err != nil {
		log.Printf("Error parsing Twilio webhook: %v", err)
		return events.LambdaFunctionURLResponse{
			StatusCode: 400,
			Body:       fmt.Sprintf("Error parsing webhook: %v", err),
			Headers: map[string]string{
				"Content-Type": "text/plain",
			},
		}, nil
	}
	
	log.Printf("Parsed Twilio error: CallSid=%s, ErrorCode=%d, ErrorMessage=%s, Severity=%s", 
		twilioError.CallSid, twilioError.ErrorCode, twilioError.ErrorMessage, twilioError.Severity())
	
	// Get the appropriate alert handler
	alertHandler, err := handlers.GetAlertHandler()
	if err != nil {
		log.Printf("Error getting alert handler: %v", err)
		return events.LambdaFunctionURLResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf("Error configuring alerts: %v", err),
			Headers: map[string]string{
				"Content-Type": "text/plain",
			},
		}, nil
	}
	
	// Send the alert
	if err := alertHandler.SendAlert(ctx, twilioError); err != nil {
		log.Printf("Error sending alert: %v", err)
		return events.LambdaFunctionURLResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf("Error sending alert: %v", err),
			Headers: map[string]string{
				"Content-Type": "text/plain",
			},
		}, nil
	}
	
	log.Printf("Successfully processed Twilio error webhook for CallSid=%s", twilioError.CallSid)
	
	// Return success response
	return events.LambdaFunctionURLResponse{
		StatusCode: 200,
		Body:       "OK",
		Headers: map[string]string{
			"Content-Type": "text/plain",
		},
	}, nil
}

// isValidTwilioErrorWebhook performs basic validation to ensure this is a Twilio error webhook
func isValidTwilioErrorWebhook(formData url.Values) bool {
	// Must have ErrorCode and ErrorMessage
	errorCode := formData.Get("ErrorCode")
	errorMessage := formData.Get("ErrorMessage")
	
	if errorCode == "" || errorMessage == "" {
		return false
	}
	
	// Optional: Check for AccountSid format (starts with AC)
	accountSid := formData.Get("AccountSid")
	if accountSid != "" && !strings.HasPrefix(accountSid, "AC") {
		return false
	}
	
	// Optional: Check for CallSid format (starts with CA)
	callSid := formData.Get("CallSid")
	if callSid != "" && !strings.HasPrefix(callSid, "CA") {
		return false
	}
	
	return true
}

func main() {
	lambda.Start(handler)
}