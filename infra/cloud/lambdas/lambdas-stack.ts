import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as path from 'path';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as snsSubscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import { SharedStacks } from '../shared/shared-config';

export class LambdasStack extends cdk.Stack {
    constructor(scope: Construct, id: string, sharedStacks: SharedStacks, props?: cdk.StackProps) {
        super(scope, id, props);

        // Use the shared VPC
        const vpc = sharedStacks.vpc.vpc;

        const securityGroup = new ec2.SecurityGroup(this, 'LambdaSecurityGroup', {
            vpc,
            description: 'Security group for Lambdas in VPC',
            allowAllOutbound: true
        });

        // ARN of the secret for DB credentials
        const secretArn = 'arn:aws:secretsmanager:us-west-2:816069150268:secret:PostgresDBCredentials-tqW9hm'; 

        // SNS topic for the Camera events
        const snsTopic = sns.Topic.fromTopicArn(this, 'CameraStreamObjectDetectionAlerts', 'arn:aws:sns:us-west-2:816069150268:CameraStreamObjectDetectionAlerts');

        // S3 bucket for storing the video clips
        const s3Bucket = s3.Bucket.fromBucketName(this, 'CameraStreamStorageBucket', 'camera-stream-storage');

        // SNS-to-FieldReport Lambda
        const snsToSituationLambda = new lambda.Function(this, 'CameraListenerSituationLambda', {
            functionName: 'CameraListenerSituationLambda',
            runtime: lambda.Runtime.PROVIDED_AL2023,
            handler: 'main',
            code: lambda.Code.fromAsset(path.join(__dirname, './camera-listener/sns-to-situation/bootstrap.zip')),
            vpc, 
            vpcSubnets: { subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS }, 
            securityGroups: [securityGroup]
        });

        // SNS-to-DB Lambda
        const snsToDBLambda = new lambda.Function(this, 'CameraListenerDBLambda', {
            functionName: 'CameraListenerDBLambda',
            runtime: lambda.Runtime.PROVIDED_AL2023, 
            handler: 'main', 
            code: lambda.Code.fromAsset(path.join(__dirname, './camera-listener/sns-to-db/bootstrap.zip')), 
            vpc,
            vpcSubnets: { subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS },
            securityGroups: [securityGroup],
            environment: {
                SECRET_ARN: secretArn, 
                DB_SSL_MODE: 'require'
            }
        });

        // SNS-to-S3 Lambda
        const snsToS3Lambda = new lambda.Function(this, 'CameraListenerS3Lambda', {
            functionName: 'CameraListenerS3Lambda',
            runtime: lambda.Runtime.PROVIDED_AL2023, 
            handler: 'main', 
            code: lambda.Code.fromAsset(path.join(__dirname, './camera-listener/sns-to-s3/bootstrap.zip')), 
            vpc,
            vpcSubnets: { subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS },
            securityGroups: [securityGroup],
            timeout: cdk.Duration.seconds(60),
            environment: {
                S3_BUCKET: 'camera-stream-storage'
            }
        });

    // Grant Lambda permissions to AWS Secrets Manager for DB Lambda
        snsToDBLambda.addToRolePolicy(new iam.PolicyStatement({
            actions: ['secretsmanager:GetSecretValue'],
            resources: [secretArn]
        }));

        // Allow Lambda to describe RDS instances
        snsToDBLambda.addToRolePolicy(new iam.PolicyStatement({
            actions: ['rds:DescribeDBInstances', 'rds:DescribeDBClusters'],
            resources: ['*']
        }));

        // Grant IAM permissions for SNS-to-S3 Lambda to access Kinesis Video and S3
        snsToS3Lambda.addToRolePolicy(new iam.PolicyStatement({
            actions: [
                "kinesisvideo:*",
                "kinesisvideoarchivedmedia:*" 
            ],
            resources: [
                `arn:aws:kinesisvideo:us-west-2:816069150268:stream/*/*`
            ]
        }));

        snsToS3Lambda.addToRolePolicy(new iam.PolicyStatement({
            actions: ['s3:PutObject'],
            resources: [`${s3Bucket.bucketArn}/*`]
        }));

        // Attach SNS Topic to Lambdas (Subscribe them to receive SNS events)
        snsTopic.addSubscription(new snsSubscriptions.LambdaSubscription(snsToSituationLambda));
        snsTopic.addSubscription(new snsSubscriptions.LambdaSubscription(snsToDBLambda));
        snsTopic.addSubscription(new snsSubscriptions.LambdaSubscription(snsToS3Lambda));

        // Twilio Error Monitor Lambda with Function URL
        const twilioMonitorLambda = new lambda.Function(this, 'TwilioMonitorLambda', {
            functionName: 'TwilioMonitorLambda',
            runtime: lambda.Runtime.PROVIDED_AL2023,
            handler: 'bootstrap',
            code: lambda.Code.fromAsset(path.join(__dirname, './twilio-monitor/bootstrap.zip')),
            timeout: cdk.Duration.seconds(30),
            environment: {
                // Default to Slack, can be overridden per environment
                ALERT_TYPE: 'slack',
                // These will be set per environment via CDK context or environment variables
                SLACK_WEBHOOK_URL: process.env.SLACK_WEBHOOK_URL || '',
                PAGERDUTY_ROUTING_KEY: process.env.PAGERDUTY_ROUTING_KEY || '',
                SENTRY_DSN: process.env.SENTRY_DSN || '',
            }
        });

        // Create Function URL for direct webhook access
        const functionUrl = twilioMonitorLambda.addFunctionUrl({
            authType: lambda.FunctionUrlAuthType.NONE,
            cors: {
                allowedOrigins: ['*'],
                allowedMethods: [lambda.HttpMethod.POST],
                allowedHeaders: ['Content-Type'],
            },
        });

        // Output the Function URL for easy access
        new cdk.CfnOutput(this, 'TwilioWebhookURL', {
            value: functionUrl.url,
            description: 'Twilio Error Webhook URL',
            exportName: 'TwilioWebhookURL',
        });
    }
}