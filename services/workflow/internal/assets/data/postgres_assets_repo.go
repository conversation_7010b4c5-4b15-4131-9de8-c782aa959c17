package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	cmncontext "common/context"
	database "common/database"

	assets "proto/hero/assets/v2"

	commonUtils "common/utils"

	"github.com/google/uuid"
)

// Asset search field constants to avoid repetition
const (
	FieldID           = "id"
	FieldName         = "name"
	FieldContactNo    = "contact_no"
	FieldContactEmail = "contact_email"
)

// PostgresAssetRepository implements AssetRepository using PostgreSQL.
type PostgresAssetRepository struct {
	database *sql.DB
}

// NewPostgresAssetRepository creates a new repository backed by PostgreSQL.
func NewPostgresAssetRepository(database *sql.DB) *PostgresAssetRepository {
	return &PostgresAssetRepository{database: database}
}

// getCognitoJwtSubFromContext retrieves the current cognitoJwtSubAsUsername from context.
func getCognitoJwtSubFromContext(context context.Context) (string, error) {
	cognitoJwtSub := cmncontext.GetUsername(context)
	if cognitoJwtSub == "" {
		return "", errors.New("cognitoJwtSub not found in context")
	}
	if !strings.HasPrefix(cognitoJwtSub, "cognito:") {
		return "", errors.New("cognitoJwtSub is not in cognito:<id> format")
	}
	cognitoJwtSub = strings.TrimPrefix(cognitoJwtSub, "cognito:")
	return cognitoJwtSub, nil
}

// CreateAsset creates a new asset record using a prepared statement.
func (postgresAssetRepository *PostgresAssetRepository) CreateAsset(ctx context.Context, transaction *sql.Tx, assetRecord *assets.Asset) error {
	return database.WithSessionErr(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) error {
		// Generate an ID if not provided.
		if assetRecord.Id == "" {
			assetRecord.Id = uuid.New().String()
		}

		// If the asset type is test, we can take the cognitoJwtSub from the context if not provided.
		// Do not do this for other asset types.
		if assetRecord.CognitoJwtSub == "" && assetRecord.Type == assets.AssetType_ASSET_TYPE_TEST {
			cognitoJwtSub, err := getCognitoJwtSubFromContext(ctx)
			if err != nil {
				return err
			}
			assetRecord.CognitoJwtSub = cognitoJwtSub
		}

		currentTime := time.Now()
		assetRecord.CreateTime = commonUtils.TimeToISO8601String(currentTime)
		assetRecord.UpdateTime = commonUtils.TimeToISO8601String(currentTime)
		assetRecord.StatusChangedTime = commonUtils.TimeToISO8601String(currentTime)

		if assetRecord.AdditionalInfoJson == "" {
			assetRecord.AdditionalInfoJson = "{}"
		}

		// Enforce resource_type to be "ASSET".
		assetRecord.ResourceType = FixedResourceTypeAsset

		insertQuery := `
			INSERT INTO assets
			(id, cognito_jwt_sub, org_id, name, type, status, latitude, longitude, location_update_time, contact_no, contact_email, create_time, update_time, resource_type, additional_info_json, status_changed_time)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
		`

		preparedStatement, err := tx.PrepareContext(ctx, insertQuery)
		if err != nil {
			return err
		}
		defer preparedStatement.Close()

		// Convert ISO8601 string timestamps to time.Time for database storage
		var createTime, updateTime, statusChangedTime time.Time
		var locationUpdateTime sql.NullTime
		var timeErr error

		// Required timestamps
		createTime, timeErr = commonUtils.ISO8601StringToTime(assetRecord.CreateTime)
		if timeErr != nil {
			return fmt.Errorf("invalid create_time format: %w", timeErr)
		}

		updateTime, timeErr = commonUtils.ISO8601StringToTime(assetRecord.UpdateTime)
		if timeErr != nil {
			return fmt.Errorf("invalid update_time format: %w", timeErr)
		}

		statusChangedTime, timeErr = commonUtils.ISO8601StringToTime(assetRecord.StatusChangedTime)
		if timeErr != nil {
			return fmt.Errorf("invalid status_changed_time format: %w", timeErr)
		}

		// Optional timestamps
		if assetRecord.LocationUpdateTime != "" {
			t, timeErr := commonUtils.ISO8601StringToTime(assetRecord.LocationUpdateTime)
			if timeErr != nil {
				return fmt.Errorf("invalid location_update_time format: %w", timeErr)
			}
			locationUpdateTime = sql.NullTime{Time: t, Valid: true}
		}

		_, executionError := preparedStatement.ExecContext(ctx,
			assetRecord.Id,
			assetRecord.CognitoJwtSub,
			assetRecord.OrgId,
			assetRecord.Name,
			int32(assetRecord.Type),
			int32(assetRecord.Status),
			assetRecord.Latitude,
			assetRecord.Longitude,
			locationUpdateTime,
			assetRecord.ContactNo,
			assetRecord.ContactEmail,
			createTime,
			updateTime,
			assetRecord.ResourceType,
			assetRecord.AdditionalInfoJson,
			statusChangedTime,
		)
		return executionError
	})
}

// GetAsset retrieves an asset by its ID.
func (postgresAssetRepository *PostgresAssetRepository) GetAsset(ctx context.Context, transaction *sql.Tx, assetID string) (*assets.Asset, error) {
	return database.WithSession(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) (*assets.Asset, error) {
		selectQuery := `
			SELECT id, org_id, cognito_jwt_sub, name, type, status, latitude, longitude, location_update_time, 
			       contact_no, contact_email, create_time, update_time, resource_type, 
			       additional_info_json, status_changed_time
			FROM assets
			WHERE id = $1
		`
		queryRow := tx.QueryRowContext(ctx, selectQuery, assetID)

		assetRecord := &assets.Asset{}
		var assetTypeValue, assetStatusValue int32
		var creationTimestamp, updateTimestamp time.Time
		var locationUpdateTimestamp sql.NullTime
		var additionalInformation string
		// Use sql.NullTime for status_changed_time to handle potential NULLs.
		var statusChangedTimestamp sql.NullTime

		scanError := queryRow.Scan(
			&assetRecord.Id,
			&assetRecord.OrgId,
			&assetRecord.CognitoJwtSub,
			&assetRecord.Name,
			&assetTypeValue,
			&assetStatusValue,
			&assetRecord.Latitude,
			&assetRecord.Longitude,
			&locationUpdateTimestamp,
			&assetRecord.ContactNo,
			&assetRecord.ContactEmail,
			&creationTimestamp,
			&updateTimestamp,
			&assetRecord.ResourceType,
			&additionalInformation,
			&statusChangedTimestamp,
		)
		if scanError != nil {
			if errors.Is(scanError, sql.ErrNoRows) {
				return nil, ErrAssetNotFound
			}
			return nil, scanError
		}
		assetRecord.Type = assets.AssetType(assetTypeValue)
		assetRecord.Status = assets.AssetStatus(assetStatusValue)
		if locationUpdateTimestamp.Valid {
			assetRecord.LocationUpdateTime = commonUtils.TimeToISO8601String(locationUpdateTimestamp.Time)
		} else {
			assetRecord.LocationUpdateTime = ""
		}
		assetRecord.CreateTime = commonUtils.TimeToISO8601String(creationTimestamp)
		assetRecord.UpdateTime = commonUtils.TimeToISO8601String(updateTimestamp)
		assetRecord.AdditionalInfoJson = additionalInformation

		// Set StatusChangedTime only if valid.
		if statusChangedTimestamp.Valid {
			assetRecord.StatusChangedTime = commonUtils.TimeToISO8601String(statusChangedTimestamp.Time)
		} else {
			assetRecord.StatusChangedTime = ""
		}
		return assetRecord, nil
	})
}

// GetAssetByCognitoSub retrieves an asset by its Cognito JWT sub claim.
func (postgresAssetRepository *PostgresAssetRepository) GetAssetByCognitoSub(ctx context.Context, transaction *sql.Tx, cognitoSub string) (*assets.Asset, error) {
	return database.WithSession(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) (*assets.Asset, error) {
		selectQuery := `
			SELECT id, org_id, cognito_jwt_sub, name, type, status, latitude, longitude, location_update_time, 
			       contact_no, contact_email, create_time, update_time, resource_type, 
			       additional_info_json, status_changed_time
			FROM assets
			WHERE cognito_jwt_sub = $1
		`
		queryRow := tx.QueryRowContext(ctx, selectQuery, cognitoSub)

		assetRecord := &assets.Asset{}
		var assetTypeValue, assetStatusValue int32
		var creationTimestamp, updateTimestamp time.Time
		var locationUpdateTimestamp sql.NullTime
		var additionalInformation string
		var statusChangedTimestamp sql.NullTime

		scanError := queryRow.Scan(
			&assetRecord.Id,
			&assetRecord.OrgId,
			&assetRecord.CognitoJwtSub,
			&assetRecord.Name,
			&assetTypeValue,
			&assetStatusValue,
			&assetRecord.Latitude,
			&assetRecord.Longitude,
			&locationUpdateTimestamp,
			&assetRecord.ContactNo,
			&assetRecord.ContactEmail,
			&creationTimestamp,
			&updateTimestamp,
			&assetRecord.ResourceType,
			&additionalInformation,
			&statusChangedTimestamp,
		)
		if scanError != nil {
			if errors.Is(scanError, sql.ErrNoRows) {
				return nil, ErrAssetNotFound
			}
			return nil, scanError
		}
		assetRecord.Type = assets.AssetType(assetTypeValue)
		assetRecord.Status = assets.AssetStatus(assetStatusValue)
		if locationUpdateTimestamp.Valid {
			assetRecord.LocationUpdateTime = commonUtils.TimeToISO8601String(locationUpdateTimestamp.Time)
		} else {
			assetRecord.LocationUpdateTime = ""
		}
		assetRecord.CreateTime = commonUtils.TimeToISO8601String(creationTimestamp)
		assetRecord.UpdateTime = commonUtils.TimeToISO8601String(updateTimestamp)
		assetRecord.AdditionalInfoJson = additionalInformation

		if statusChangedTimestamp.Valid {
			assetRecord.StatusChangedTime = commonUtils.TimeToISO8601String(statusChangedTimestamp.Time)
		} else {
			assetRecord.StatusChangedTime = ""
		}
		return assetRecord, nil
	})
}

// ListAssetsByPhoneNumber returns all assets associated with a phone number.
// Note: We intentionally don't use LIMIT 1 here as multiple assets can share the same phone number.
func (postgresAssetRepository *PostgresAssetRepository) ListAssetsByPhoneNumber(ctx context.Context, transaction *sql.Tx, phoneNumber string) ([]*assets.Asset, error) {
	if phoneNumber != "" {
		standardized, err := commonUtils.StandardizeUSPhoneNumber(phoneNumber)
		if err != nil {
			return nil, err
		}
		phoneNumber = standardized
	}

	return database.WithSession(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) ([]*assets.Asset, error) {
		selectQuery := `
			SELECT id, org_id, cognito_jwt_sub, name, type, status, latitude, longitude, location_update_time, contact_no, contact_email, create_time, update_time, resource_type, additional_info_json
			FROM assets
			WHERE contact_no = $1
		`
		rows, err := tx.QueryContext(ctx, selectQuery, phoneNumber)
		if err != nil {
			return nil, err
		}
		defer rows.Close()

		var assetRecords []*assets.Asset
		for rows.Next() {
			assetRecord := &assets.Asset{}
			var assetTypeValue, assetStatusValue int32
			var creationTimestamp, updateTimestamp time.Time
			var locationUpdateTimestamp sql.NullTime
			var additionalInformation string

			scanError := rows.Scan(
				&assetRecord.Id,
				&assetRecord.OrgId,
				&assetRecord.CognitoJwtSub,
				&assetRecord.Name,
				&assetTypeValue,
				&assetStatusValue,
				&assetRecord.Latitude,
				&assetRecord.Longitude,
				&locationUpdateTimestamp,
				&assetRecord.ContactNo,
				&assetRecord.ContactEmail,
				&creationTimestamp,
				&updateTimestamp,
				&assetRecord.ResourceType,
				&additionalInformation,
			)
			if scanError != nil {
				return nil, scanError
			}

			assetRecord.Type = assets.AssetType(assetTypeValue)
			assetRecord.Status = assets.AssetStatus(assetStatusValue)
			if locationUpdateTimestamp.Valid {
				assetRecord.LocationUpdateTime = commonUtils.TimeToISO8601String(locationUpdateTimestamp.Time)
			} else {
				assetRecord.LocationUpdateTime = ""
			}
			assetRecord.CreateTime = commonUtils.TimeToISO8601String(creationTimestamp)
			assetRecord.UpdateTime = commonUtils.TimeToISO8601String(updateTimestamp)
			assetRecord.AdditionalInfoJson = additionalInformation

			assetRecords = append(assetRecords, assetRecord)
		}

		// Check for any errors during iteration
		if rowErr := rows.Err(); rowErr != nil {
			return nil, rowErr
		}

		if len(assetRecords) == 0 {
			return nil, ErrAssetNotFound
		}

		return assetRecords, nil
	})
}

// ListAssets returns a paginated list of assets ordered by create_time and ID.
func (postgresAssetRepository *PostgresAssetRepository) ListAssets(ctx context.Context, transaction *sql.Tx, pageSize int, pageToken string, assetType assets.AssetType, assetStatus assets.AssetStatus, orderBy string) (*PaginatedAssets, error) {
	return database.WithSession(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) (*PaginatedAssets, error) {
		// Calculate offset from pageToken
		offset := 0
		if pageToken != "" {
			if convertedOffset, err := strconv.Atoi(pageToken); err == nil {
				offset = convertedOffset
			}
		}

		// Base query and slice for SQL conditions and arguments
		baseQuery := `
		SELECT id, org_id, cognito_jwt_sub, name, type, status, latitude, longitude, location_update_time, 
		       contact_no, contact_email, create_time, update_time, resource_type, 
		       additional_info_json, status_changed_time
		FROM assets
	`
		var conditions []string
		var args []interface{}

		// Add filtering by asset type if specified (non-zero implies a valid filter)
		if assetType != assets.AssetType_ASSET_TYPE_UNSPECIFIED {
			conditions = append(conditions, "type = $"+strconv.Itoa(len(args)+1))
			args = append(args, int32(assetType))
		}

		// Add filtering by asset status if specified (non-zero implies a valid filter)
		if assetStatus != assets.AssetStatus_ASSET_STATUS_UNSPECIFIED {
			conditions = append(conditions, "status = $"+strconv.Itoa(len(args)+1))
			args = append(args, int32(assetStatus))
		}

		// Append WHERE clause if any filtering conditions are present
		query := baseQuery
		if len(conditions) > 0 {
			query += " WHERE " + strings.Join(conditions, " AND ")
		}

		// Apply ordering: if provided, use it; otherwise default to create_time and id.
		if orderBy != "" {
			query += " ORDER BY " + orderBy
		} else {
			query += " ORDER BY create_time DESC, id DESC"
		}

		// Append LIMIT and OFFSET clauses. Their parameter indices follow the previous args.
		args = append(args, pageSize, offset)
		query += " LIMIT $" + strconv.Itoa(len(args)-1) + " OFFSET $" + strconv.Itoa(len(args))

		// Execute the query
		queryRows, queryError := tx.QueryContext(ctx, query, args...)
		if queryError != nil {
			return nil, queryError
		}
		defer queryRows.Close()

		// Process the returned rows.
		var assetRecords []*assets.Asset
		for queryRows.Next() {
			currentAssetRecord := &assets.Asset{}
			var assetTypeValue, assetStatusValue int32
			var creationTimestamp, updateTimestamp time.Time
			var locationUpdateTimestamp sql.NullTime
			var statusChangedTimestamp sql.NullTime
			var additionalInformation string
			if scanError := queryRows.Scan(
				&currentAssetRecord.Id,
				&currentAssetRecord.OrgId,
				&currentAssetRecord.CognitoJwtSub,
				&currentAssetRecord.Name,
				&assetTypeValue,
				&assetStatusValue,
				&currentAssetRecord.Latitude,
				&currentAssetRecord.Longitude,
				&locationUpdateTimestamp,
				&currentAssetRecord.ContactNo,
				&currentAssetRecord.ContactEmail,
				&creationTimestamp,
				&updateTimestamp,
				&currentAssetRecord.ResourceType,
				&additionalInformation,
				&statusChangedTimestamp,
			); scanError != nil {
				return nil, scanError
			}
			currentAssetRecord.Type = assets.AssetType(assetTypeValue)
			currentAssetRecord.Status = assets.AssetStatus(assetStatusValue)
			if locationUpdateTimestamp.Valid {
				currentAssetRecord.LocationUpdateTime = commonUtils.TimeToISO8601String(locationUpdateTimestamp.Time)
			} else {
				currentAssetRecord.LocationUpdateTime = ""
			}
			currentAssetRecord.CreateTime = commonUtils.TimeToISO8601String(creationTimestamp)
			currentAssetRecord.UpdateTime = commonUtils.TimeToISO8601String(updateTimestamp)
			currentAssetRecord.AdditionalInfoJson = additionalInformation
			if statusChangedTimestamp.Valid {
				currentAssetRecord.StatusChangedTime = commonUtils.TimeToISO8601String(statusChangedTimestamp.Time)
			} else {
				currentAssetRecord.StatusChangedTime = ""
			}
			assetRecords = append(assetRecords, currentAssetRecord)
		}

		if rowsError := queryRows.Err(); rowsError != nil {
			return nil, rowsError
		}

		// Compute next page token if there might be more records.
		var nextPageToken string
		if len(assetRecords) == pageSize {
			nextPageToken = strconv.Itoa(offset + pageSize)
		}
		return &PaginatedAssets{
			Assets:        assetRecords,
			NextPageToken: nextPageToken,
		}, nil
	})
}

// UpdateAsset updates the fields of an existing asset.
func (postgresAssetRepository *PostgresAssetRepository) UpdateAsset(ctx context.Context, transaction *sql.Tx, updatedAsset *assets.Asset) (*assets.Asset, error) {
	return database.WithSession(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) (*assets.Asset, error) {
		currentTime := time.Now()
		updatedAsset.UpdateTime = commonUtils.TimeToISO8601String(currentTime)
		// Enforce resource_type to always be "ASSET".
		updatedAsset.ResourceType = "ASSET"
		if updatedAsset.AdditionalInfoJson == "" {
			updatedAsset.AdditionalInfoJson = "{}"
		}

		// Retrieve the existing asset to check if the status is changing.
		existingAsset, err := postgresAssetRepository.GetAsset(ctx, tx, updatedAsset.Id)
		if err != nil {
			return nil, err
		}

		var newStatusChangedTime time.Time
		if int32(updatedAsset.Status) != int32(existingAsset.Status) {
			newStatusChangedTime = currentTime
		} else {
			// Convert from ISO8601 string to time.Time
			newStatusChangedTime, _ = commonUtils.ISO8601StringToTime(existingAsset.StatusChangedTime)
		}

		updateQuery := `
			UPDATE assets
			SET cognito_jwt_sub = $1, name = $2, type = $3, status = $4, latitude = $5, longitude = $6, 
				location_update_time = $7, contact_no = $8, contact_email = $9, update_time = $10, resource_type = $11, additional_info_json = $12, status_changed_time = $13
			WHERE id = $14
		`
		preparedStatement, err := tx.PrepareContext(ctx, updateQuery)
		if err != nil {
			return nil, err
		}
		defer preparedStatement.Close()

		// Convert ISO8601 string timestamps to time.Time for database storage
		var updateTime time.Time
		var locationUpdateTime sql.NullTime

		// Required timestamp
		var timeErr error
		updateTime, timeErr = commonUtils.ISO8601StringToTime(updatedAsset.UpdateTime)
		if timeErr != nil {
			return nil, fmt.Errorf("invalid update_time format: %w", timeErr)
		}

		// Optional timestamps
		if updatedAsset.LocationUpdateTime != "" {
			t, timeErr := commonUtils.ISO8601StringToTime(updatedAsset.LocationUpdateTime)
			if timeErr != nil {
				return nil, fmt.Errorf("invalid location_update_time format: %w", timeErr)
			}
			locationUpdateTime = sql.NullTime{Time: t, Valid: true}
		}

		_, executionError := preparedStatement.ExecContext(ctx,
			updatedAsset.CognitoJwtSub,
			updatedAsset.Name,
			int32(updatedAsset.Type),
			int32(updatedAsset.Status),
			updatedAsset.Latitude,
			updatedAsset.Longitude,
			locationUpdateTime,
			updatedAsset.ContactNo,
			updatedAsset.ContactEmail,
			updateTime,
			updatedAsset.ResourceType,
			updatedAsset.AdditionalInfoJson,
			newStatusChangedTime,
			updatedAsset.Id,
		)
		if executionError != nil {
			return nil, executionError
		}
		return postgresAssetRepository.GetAsset(ctx, tx, updatedAsset.Id)
	})
}

// DeleteAsset physically deletes an asset from the database.
func (postgresAssetRepository *PostgresAssetRepository) DeleteAsset(ctx context.Context, transaction *sql.Tx, assetID string) error {
	return database.WithSessionErr(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) error {
		deleteQuery := `
			DELETE FROM assets
			WHERE id = $1
		`
		executionResult, executionError := tx.ExecContext(ctx, deleteQuery, assetID)
		if executionError != nil {
			return executionError
		}
		rowsAffected, rowsAffectedError := executionResult.RowsAffected()
		if rowsAffectedError != nil {
			return rowsAffectedError
		}
		if rowsAffected == 0 {
			return ErrAssetNotFound
		}
		return nil
	})
}

func (postgresAssetRepository *PostgresAssetRepository) GetZelloChannels(ctx context.Context, transaction *sql.Tx) ([]*assets.ZelloChannel, error) {
	return database.WithSession(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) ([]*assets.ZelloChannel, error) {
		selectQuery := `
			SELECT id, org_id, zello_channel_id, display_name
			FROM zello_channels
		`
		queryRows, queryError := tx.QueryContext(ctx, selectQuery)
		if queryError != nil {
			return nil, queryError
		}
		defer queryRows.Close()

		var zelloChannels []*assets.ZelloChannel
		for queryRows.Next() {
			var zelloChannel assets.ZelloChannel
			if scanError := queryRows.Scan(&zelloChannel.Id, &zelloChannel.OrgId, &zelloChannel.ZelloChannelId, &zelloChannel.DisplayName); scanError != nil {
				return nil, scanError
			}
			zelloChannels = append(zelloChannels, &zelloChannel)
		}
		if rowsError := queryRows.Err(); rowsError != nil {
			return nil, rowsError
		}
		return zelloChannels, nil
	})
}

func (postgresAssetRepository *PostgresAssetRepository) CreateZelloCreds(ctx context.Context, transaction *sql.Tx, creds *assets.ZelloCreds) error {
	return database.WithSessionErr(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) error {
		if creds.Id == "" {
			creds.Id = uuid.New().String()
		}

		insertQuery := `
			INSERT INTO zello_creds (asset_id, username, encrypted_password, org_id)
			VALUES ($1, $2, $3, $4)
		`

		preparedStatement, err := tx.PrepareContext(ctx, insertQuery)
		if err != nil {
			return err
		}
		defer preparedStatement.Close()

		_, executionError := preparedStatement.ExecContext(ctx,
			creds.AssetId,
			creds.Username,
			creds.EncryptedPassword,
			creds.OrgId,
		)
		return executionError
	})
}

func (postgresAssetRepository *PostgresAssetRepository) GetZelloCreds(ctx context.Context, transaction *sql.Tx, assetID string) (*assets.ZelloCreds, error) {
	return database.WithSession(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) (*assets.ZelloCreds, error) {
		selectQuery := `
			SELECT asset_id, username, encrypted_password 
			FROM zello_creds 
			WHERE asset_id = $1
		`
		queryRow := tx.QueryRowContext(ctx, selectQuery, assetID)

		var creds assets.ZelloCreds
		scanError := queryRow.Scan(
			&creds.AssetId,
			&creds.Username,
			&creds.EncryptedPassword,
		)
		if scanError != nil {
			if errors.Is(scanError, sql.ErrNoRows) {
				return nil, ErrAssetNotFound
			}
			return nil, scanError
		}
		return &creds, nil
	})
}

func (postgresAssetRepository *PostgresAssetRepository) UpdateZelloCreds(ctx context.Context, transaction *sql.Tx, zelloCreds *assets.ZelloCreds) error {
	return database.WithSessionErr(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) error {
		updateQuery := `
			UPDATE zello_creds 
			SET username = $2, encrypted_password = $3 
			WHERE asset_id = $1
		`
		preparedStatement, err := tx.PrepareContext(ctx, updateQuery)
		if err != nil {
			return err
		}
		defer preparedStatement.Close()

		_, executionError := preparedStatement.ExecContext(ctx,
			zelloCreds.AssetId,
			zelloCreds.Username,
			zelloCreds.EncryptedPassword,
		)
		return executionError
	})
}

func (postgresAssetRepository *PostgresAssetRepository) DeleteZelloCreds(ctx context.Context, transaction *sql.Tx, assetID string) error {
	return database.WithSessionErr(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) error {
		deleteQuery := `
			DELETE FROM zello_creds
			WHERE asset_id = $1
		`
		executionResult, executionError := tx.ExecContext(ctx, deleteQuery, assetID)
		if executionError != nil {
			return executionError
		}
		rowsAffected, rowsAffectedError := executionResult.RowsAffected()
		if rowsAffectedError != nil {
			return rowsAffectedError
		}
		if rowsAffected == 0 {
			return ErrAssetNotFound
		}
		return nil
	})
}

// UpdateAdditionalInfoJSON updates the AdditionalInfoJson field of the asset in PostgreSQL.
func (postgresAssetRepository *PostgresAssetRepository) UpdateAdditionalInfoJSON(ctx context.Context, transaction *sql.Tx, assetID string, additionalInfoJSON string) (string, error) {
	return database.WithSession(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) (string, error) {
		currentTime := time.Now()

		updateQuery := `
			UPDATE assets
			SET additional_info_json = $1, update_time = $2
			WHERE id = $3
		`
		result, err := tx.ExecContext(ctx, updateQuery, additionalInfoJSON, currentTime, assetID)
		if err != nil {
			return "", err
		}
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return "", err
		}
		if rowsAffected == 0 {
			return "", ErrAssetNotFound
		}
		return assetID, nil
	})
}

// -------------------------------
// Asset Search Operations
// -------------------------------

// SearchAssets performs advanced search on assets with text matching, filtering, and geographic queries.
// This method provides comprehensive search capabilities including:
// - Partial text matching on id, name, contact_no, contact_email (using trigram indexes)
// - Exact filtering by asset type and status
// - Date range filtering on all timestamp fields
// - Geographic bounding box searches
// - Configurable ordering and pagination
// - Search result highlighting for matched terms
//
// The implementation leverages PostgreSQL's trigram indexes for fast ILIKE queries,
// B-tree indexes for date ranges, and coordinate indexes for geographic searches.
// All queries are organization-scoped for security and performance.
func (postgresAssetRepository *PostgresAssetRepository) SearchAssets(requestContext context.Context, databaseTransaction *sql.Tx, searchRequest *assets.SearchAssetsRequest) (*assets.SearchAssetsResponse, error) {
	return database.WithSession(postgresAssetRepository.database, requestContext, databaseTransaction, func(transaction *sql.Tx) (*assets.SearchAssetsResponse, error) {
		// Step 1: Retrieve organization ID from context for security isolation.
		// All asset queries must be scoped to the requesting organization to ensure data isolation.
		organizationID := cmncontext.GetOrgId(requestContext)

		// Step 2: Build the comprehensive search query with all filters and conditions.
		// This constructs the base SQL query, WHERE conditions, and parameterized arguments.
		// Also populates searchTermsMap for later use in result highlighting.
		var searchTermsMap map[string][]string
		baseQueryString, whereConditionsList, queryArguments := postgresAssetRepository.buildAssetSearchQuery(searchRequest, organizationID, &searchTermsMap)

		// Step 3: Set pagination defaults and validate bounds.
		// Default page size is 20, maximum is 100 to prevent resource exhaustion.
		pageSize := int(searchRequest.PageSize)
		if pageSize <= 0 {
			pageSize = 20 // Default page size for reasonable response times
		}
		if pageSize > 100 {
			pageSize = 100 // Maximum page size to prevent memory/performance issues
		}

		// Step 4: Calculate offset from page token for pagination.
		// Page token is a simple integer offset encoded as string for stateless pagination.
		offset := 0
		if searchRequest.PageToken != "" {
			if convertedOffset, parseError := strconv.Atoi(searchRequest.PageToken); parseError == nil {
				offset = convertedOffset
			}
			// Invalid page tokens are silently ignored, defaulting to offset 0
		}

		// Step 5: Build ORDER BY clause based on search criteria.
		// Supports ordering by relevance, name, and various timestamp fields.
		// Always includes ID as secondary sort for consistent pagination.
		orderByClause := postgresAssetRepository.buildAssetOrderByClause(searchRequest.OrderBy, searchRequest.Ascending)

		// Step 6: Complete the query with ordering and pagination clauses.
		// Append LIMIT and OFFSET clauses. Their parameter indices follow the previous args.
		var fullQuery string
		if len(whereConditionsList) > 0 {
			fullQuery = baseQueryString + " AND " + strings.Join(whereConditionsList, " AND ") +
				" " + orderByClause +
				" LIMIT $" + strconv.Itoa(len(queryArguments)+1) +
				" OFFSET $" + strconv.Itoa(len(queryArguments)+2)
		} else {
			fullQuery = baseQueryString +
				" " + orderByClause +
				" LIMIT $" + strconv.Itoa(len(queryArguments)+1) +
				" OFFSET $" + strconv.Itoa(len(queryArguments)+2)
		}
		queryArguments = append(queryArguments, pageSize, offset)

		// Step 7: Execute the search query against the database.
		// Use the transaction provided by WithSession for consistency.
		databaseRows, queryError := transaction.QueryContext(requestContext, fullQuery, queryArguments...)
		if queryError != nil {
			return nil, fmt.Errorf("failed to execute asset search query: %w", queryError)
		}
		defer databaseRows.Close() // Ensure rows are closed to prevent resource leaks

		// Step 8: Scan and collect all matching assets from the result set.
		// Each row is converted from database types to protobuf Asset objects.
		var foundAssets []*assets.Asset
		for databaseRows.Next() {
			foundAsset, scanError := postgresAssetRepository.scanAssetSearchRow(databaseRows)
			if scanError != nil {
				return nil, fmt.Errorf("failed to scan asset search row: %w", scanError)
			}
			foundAssets = append(foundAssets, foundAsset)
		}

		// Step 9: Check for any errors that occurred during row iteration.
		// This catches errors that may have occurred after QueryContext but during scanning.
		if rowsError := databaseRows.Err(); rowsError != nil {
			return nil, fmt.Errorf("error iterating asset search results: %w", rowsError)
		}

		// Step 10: Get total count of matching results for pagination metadata.
		// This executes a separate COUNT(*) query with the same WHERE conditions.
		// Excludes LIMIT/OFFSET arguments since we want the total count, not paginated count.
		totalResultCount, countError := postgresAssetRepository.getAssetSearchTotalCount(requestContext, transaction, whereConditionsList, queryArguments[:len(queryArguments)-2])
		if countError != nil {
			return nil, fmt.Errorf("failed to get total result count: %w", countError)
		}

		// Step 11: Generate search term highlights for UI display.
		// Creates highlighted fragments showing where search terms were found in asset fields.
		searchHighlights := postgresAssetRepository.generateAssetSearchHighlights(foundAssets, searchTermsMap)

		// Step 12: Calculate next page token for pagination.
		// Only provide next page token if there might be more results.
		var nextPageToken string
		if len(foundAssets) == pageSize {
			nextPageToken = strconv.Itoa(offset + pageSize)
		}

		// Step 13: Return the complete search response with all results and metadata.
		return &assets.SearchAssetsResponse{
			Assets:        foundAssets,
			NextPageToken: nextPageToken,
			Highlights:    searchHighlights,
			TotalResults:  postgresAssetRepository.safeIntToInt32(totalResultCount),
		}, nil
	})
}

// buildAssetSearchQuery constructs the SQL query for asset search with all filters and conditions.
// This method is responsible for building a comprehensive search query that supports:
// - Organization-scoped queries for multi-tenant security
// - General text search across multiple fields using trigram indexes
// - Field-specific searches with exact term matching
// - Asset type and status filtering using enum values
// - Date range filtering on all timestamp columns
// - Geographic bounding box searches using coordinate indexes
//
// The method returns three components:
// 1. baseQueryString: The SELECT clause with field list
// 2. whereConditionsList: Array of WHERE conditions to be joined with AND
// 3. queryArguments: Parameterized arguments for secure SQL execution
// 4. searchTermsMap: Map of field names to search terms for highlighting
func (postgresAssetRepository *PostgresAssetRepository) buildAssetSearchQuery(
	searchRequest *assets.SearchAssetsRequest,
	organizationID int32,
	searchTermsMap *map[string][]string,
) (baseQueryString string, whereConditionsList []string, queryArguments []interface{}) {

	// Step 1: Initialize the search terms map for later highlighting.
	// This map tracks which terms were searched in which fields for UI highlighting.
	*searchTermsMap = make(map[string][]string)

	// Step 2: Define the base SELECT query with all asset fields.
	// Selects all columns needed to construct complete Asset protobuf objects.
	// Uses table alias 'a' for consistent referencing in WHERE conditions.
	baseQueryString = `
		SELECT a.id, a.org_id, a.cognito_jwt_sub, a.name, a.type, a.status, 
		       a.latitude, a.longitude, a.location_update_time, a.contact_no, a.contact_email, 
		       a.create_time, a.update_time, a.resource_type, a.additional_info_json, a.status_changed_time
		FROM assets a
		WHERE a.org_id = $1`

	// Step 3: Initialize WHERE conditions and query arguments.
	// Start with organization filter as the first parameterized argument for security.
	whereConditionsList = []string{}
	queryArguments = []interface{}{organizationID}
	argumentCounter := 2 // Next available parameter index

	// Step 4: Handle general text search query across multiple fields.
	// This provides "search box" functionality where users can enter any text
	// and it searches across id, name, contact_no, and contact_email fields.
	if searchRequest.Query != "" {
		searchFieldsList := searchRequest.SearchFields
		if len(searchFieldsList) == 0 {
			// Default to all searchable fields if none specified
			searchFieldsList = []string{FieldID, FieldName, FieldContactNo, FieldContactEmail}
		}

		// Build text search condition using ILIKE for case-insensitive partial matching
		searchCondition, searchArguments := postgresAssetRepository.buildAssetTextSearchCondition(
			searchRequest.Query, searchFieldsList, argumentCounter)
		if searchCondition != "" {
			whereConditionsList = append(whereConditionsList, "("+searchCondition+")")
			queryArguments = append(queryArguments, searchArguments...)
			argumentCounter += len(searchArguments)

			// Store search terms for highlighting - track which fields were searched
			for _, fieldName := range searchFieldsList {
				(*searchTermsMap)[fieldName] = append((*searchTermsMap)[fieldName], searchRequest.Query)
			}
		}
	}

	// Step 5: Handle field-specific queries for targeted searches.
	// These allow searching specific fields with dedicated terms,
	// providing more precise search capabilities than general text search.
	for _, fieldQueryRequest := range searchRequest.FieldQueries {
		fieldSearchCondition, fieldSearchArguments := postgresAssetRepository.buildAssetFieldSearchCondition(
			fieldQueryRequest, argumentCounter)
		if fieldSearchCondition != "" {
			whereConditionsList = append(whereConditionsList, fieldSearchCondition)
			queryArguments = append(queryArguments, fieldSearchArguments...)
			argumentCounter += len(fieldSearchArguments)

			// Store field-specific search terms for highlighting
			(*searchTermsMap)[fieldQueryRequest.Field] = append((*searchTermsMap)[fieldQueryRequest.Field], fieldQueryRequest.Query)
		}
	}

	// Step 6: Handle asset type filters for exact matching.
	// Supports filtering by one or more asset types using IN clause.
	// Uses enum integer values for efficient database storage and querying.
	if len(searchRequest.Type) > 0 {
		typeValues := make([]interface{}, len(searchRequest.Type))
		typePlaceholders := make([]string, len(searchRequest.Type))
		for index, assetType := range searchRequest.Type {
			typeValues[index] = int32(assetType)
			typePlaceholders[index] = "$" + strconv.Itoa(argumentCounter)
			argumentCounter++
		}
		whereConditionsList = append(whereConditionsList, "a.type IN ("+strings.Join(typePlaceholders, ", ")+")")
		queryArguments = append(queryArguments, typeValues...)
	}

	// Step 7: Handle asset status filters for exact matching.
	// Supports filtering by one or more asset statuses using IN clause.
	// Uses enum integer values for efficient database storage and querying.
	if len(searchRequest.Status) > 0 {
		statusValues := make([]interface{}, len(searchRequest.Status))
		statusPlaceholders := make([]string, len(searchRequest.Status))
		for index, assetStatus := range searchRequest.Status {
			statusValues[index] = int32(assetStatus)
			statusPlaceholders[index] = "$" + strconv.Itoa(argumentCounter)
			argumentCounter++
		}
		whereConditionsList = append(whereConditionsList, "a.status IN ("+strings.Join(statusPlaceholders, ", ")+")")
		queryArguments = append(queryArguments, statusValues...)
	}

	// Step 8: Handle date range filters for all timestamp fields.
	// Each date range filter can specify 'from' and/or 'to' bounds.
	// Uses B-tree indexes on timestamp columns for efficient range queries.
	argumentCounter = postgresAssetRepository.addDateRangeFilter(&whereConditionsList, &queryArguments,
		"a.create_time", searchRequest.CreateTime, argumentCounter)
	argumentCounter = postgresAssetRepository.addDateRangeFilter(&whereConditionsList, &queryArguments,
		"a.update_time", searchRequest.UpdateTime, argumentCounter)
	argumentCounter = postgresAssetRepository.addDateRangeFilter(&whereConditionsList, &queryArguments,
		"a.status_changed_time", searchRequest.StatusChangedTime, argumentCounter)
	argumentCounter = postgresAssetRepository.addDateRangeFilter(&whereConditionsList, &queryArguments,
		"a.location_update_time", searchRequest.LocationUpdateTime, argumentCounter)

	// Step 9: Handle geographic bounding box filters for location-based searches.
	// Supports filtering assets within a rectangular geographic area.
	// Uses coordinate indexes for efficient spatial queries.
	if searchRequest.MinLatitude != 0 || searchRequest.MaxLatitude != 0 ||
		searchRequest.MinLongitude != 0 || searchRequest.MaxLongitude != 0 {
		var geoConditions []string

		// Add latitude bounds if specified
		if searchRequest.MinLatitude != 0 {
			geoConditions = append(geoConditions, "a.latitude >= $"+strconv.Itoa(argumentCounter))
			queryArguments = append(queryArguments, searchRequest.MinLatitude)
			argumentCounter++
		}
		if searchRequest.MaxLatitude != 0 {
			geoConditions = append(geoConditions, "a.latitude <= $"+strconv.Itoa(argumentCounter))
			queryArguments = append(queryArguments, searchRequest.MaxLatitude)
			argumentCounter++
		}

		// Add longitude bounds if specified
		if searchRequest.MinLongitude != 0 {
			geoConditions = append(geoConditions, "a.longitude >= $"+strconv.Itoa(argumentCounter))
			queryArguments = append(queryArguments, searchRequest.MinLongitude)
			argumentCounter++
		}
		if searchRequest.MaxLongitude != 0 {
			geoConditions = append(geoConditions, "a.longitude <= $"+strconv.Itoa(argumentCounter))
			queryArguments = append(queryArguments, searchRequest.MaxLongitude)
		}

		// Group all geographic conditions with AND for precise bounding box
		if len(geoConditions) > 0 {
			whereConditionsList = append(whereConditionsList, "("+strings.Join(geoConditions, " AND ")+")")
		}
	}

	// Step 10: Return all query components for final assembly.
	return baseQueryString, whereConditionsList, queryArguments
}

// buildAssetTextSearchCondition creates search conditions for general text queries across multiple fields.
// This method implements the core text search functionality, allowing users to search for any text
// across multiple asset fields simultaneously. The search uses PostgreSQL's ILIKE operator for
// case-insensitive partial matching, leveraging trigram indexes for performance.
//
// The method supports searching across these fields:
// - id: Asset unique identifier (exact partial match)
// - name: Asset display name (partial match)
// - contact_no: Phone number (partial match)
// - contact_email: Email address (partial match)
//
// All searches use the pattern '%term%' for substring matching, allowing users to find assets
// where the search term appears anywhere within the field content.
func (postgresAssetRepository *PostgresAssetRepository) buildAssetTextSearchCondition(
	searchQuery string,
	searchFieldsList []string,
	startArgumentIndex int,
) (conditionString string, conditionArguments []interface{}) {

	// Step 1: Validate input parameters before processing.
	// Empty query or no fields means no search conditions to build.
	if searchQuery == "" || len(searchFieldsList) == 0 {
		return "", nil
	}

	// Step 2: Initialize condition building variables.
	var searchConditionsList []string
	argumentCounter := startArgumentIndex

	// Step 3: Define allowed fields for security and performance.
	// Only these fields have trigram indexes and are safe for ILIKE searches.
	// This prevents injection attacks through field names and ensures good performance.
	allowedSearchFieldsMap := map[string]bool{
		FieldID:           true, // a.id ILIKE (trigram index)
		FieldName:         true, // a.name ILIKE (trigram index)
		FieldContactNo:    true, // a.contact_no ILIKE (trigram index)
		FieldContactEmail: true, // a.contact_email ILIKE (trigram index)
	}

	// Step 4: Build ILIKE conditions for each allowed search field.
	// Each field gets its own condition joined with OR for broad matching.
	for _, searchField := range searchFieldsList {
		if !allowedSearchFieldsMap[searchField] {
			continue // Skip unsupported fields for security
		}

		// Build field-specific search conditions with parameterized queries
		switch searchField {
		case FieldID:
			// Search asset ID with partial matching - useful for finding assets by partial ID
			searchConditionsList = append(searchConditionsList, fmt.Sprintf("a.id ILIKE $%d", argumentCounter))
			conditionArguments = append(conditionArguments, "%"+searchQuery+"%")
			argumentCounter++

		case FieldName:
			// Search asset name with partial matching - primary search field for users
			searchConditionsList = append(searchConditionsList, fmt.Sprintf("a.name ILIKE $%d", argumentCounter))
			conditionArguments = append(conditionArguments, "%"+searchQuery+"%")
			argumentCounter++

		case FieldContactNo:
			// Search phone numbers with partial matching - useful for finding assets by phone
			searchConditionsList = append(searchConditionsList, fmt.Sprintf("a.contact_no ILIKE $%d", argumentCounter))
			conditionArguments = append(conditionArguments, "%"+searchQuery+"%")
			argumentCounter++

		case FieldContactEmail:
			// Search email addresses with partial matching - useful for finding assets by email
			searchConditionsList = append(searchConditionsList, fmt.Sprintf("a.contact_email ILIKE $%d", argumentCounter))
			conditionArguments = append(conditionArguments, "%"+searchQuery+"%")
			argumentCounter++
		}
	}

	// Step 5: Join all field conditions with OR for broad search.
	// This allows the search term to match in any of the specified fields.
	if len(searchConditionsList) > 0 {
		conditionString = strings.Join(searchConditionsList, " OR ")
	}

	// Step 6: Return the complete condition and its arguments.
	return conditionString, conditionArguments
}

// buildAssetFieldSearchCondition creates search conditions for field-specific queries.
// This method handles targeted searches where users want to search a specific field
// with a specific term, providing more precise search capabilities than general text search.
// Each field-specific query is treated as an independent search condition.
//
// Supported fields for field-specific search:
// - id: Exact partial matching on asset unique identifier
// - name: Partial matching on asset display name
// - contact_no: Partial matching on phone number
// - contact_email: Partial matching on email address
//
// All field searches use ILIKE for case-insensitive partial matching with '%term%' pattern.
func (postgresAssetRepository *PostgresAssetRepository) buildAssetFieldSearchCondition(
	fieldQueryRequest *assets.FieldQuery,
	startArgumentIndex int,
) (conditionString string, conditionArguments []interface{}) {

	// Step 1: Validate that the field query has a search term.
	// Empty queries don't contribute any search conditions.
	if fieldQueryRequest.Query == "" {
		return "", nil
	}

	// Step 2: Build field-specific search conditions based on the requested field.
	// Each field uses ILIKE with parameterized queries for security and performance.
	// The field name is validated implicitly by the switch statement.
	switch fieldQueryRequest.Field {
	case FieldID:
		// Search asset ID field with partial matching
		return fmt.Sprintf("a.id ILIKE $%d", startArgumentIndex), []interface{}{"%" + fieldQueryRequest.Query + "%"}

	case FieldName:
		// Search asset name field with partial matching
		return fmt.Sprintf("a.name ILIKE $%d", startArgumentIndex), []interface{}{"%" + fieldQueryRequest.Query + "%"}

	case FieldContactNo:
		// Search contact phone number field with partial matching
		return fmt.Sprintf("a.contact_no ILIKE $%d", startArgumentIndex), []interface{}{"%" + fieldQueryRequest.Query + "%"}

	case FieldContactEmail:
		// Search contact email field with partial matching
		return fmt.Sprintf("a.contact_email ILIKE $%d", startArgumentIndex), []interface{}{"%" + fieldQueryRequest.Query + "%"}

	default:
		// Unsupported field - return empty condition for security
		return "", nil
	}
}

// addDateRangeFilter adds date range filtering conditions to the query.
// This method handles filtering assets by date ranges on timestamp fields,
// supporting 'from' (inclusive) and 'to' (inclusive) bounds. Either bound
// can be specified independently, allowing for open-ended date ranges.
//
// Supported timestamp fields:
// - create_time: When the asset was created
// - update_time: When the asset was last modified
// - status_changed_time: When the asset status was last changed
// - location_update_time: When the asset location was last updated
//
// The method uses B-tree indexes on timestamp columns for efficient range queries.
// Date values are expected to be in ISO8601 format for consistent parsing.
func (postgresAssetRepository *PostgresAssetRepository) addDateRangeFilter(
	whereConditionsList *[]string,
	queryArguments *[]interface{},
	fieldName string,
	dateRange *assets.DateRange,
	startArgumentIndex int,
) int {
	argumentCounter := startArgumentIndex

	// Step 1: Check if date range filter is provided.
	// Nil date range means no filtering on this timestamp field.
	if dateRange == nil {
		return argumentCounter
	}

	// Step 2: Add 'from' date condition if specified.
	// This creates a greater-than-or-equal condition for the start of the range.
	// Uses parameterized query for security and proper date handling.
	if dateRange.From != "" {
		*whereConditionsList = append(*whereConditionsList, fieldName+" >= $"+strconv.Itoa(argumentCounter))
		*queryArguments = append(*queryArguments, dateRange.From)
		argumentCounter++
	}

	// Step 3: Add 'to' date condition if specified.
	// This creates a less-than-or-equal condition for the end of the range.
	// Uses parameterized query for security and proper date handling.
	if dateRange.To != "" {
		*whereConditionsList = append(*whereConditionsList, fieldName+" <= $"+strconv.Itoa(argumentCounter))
		*queryArguments = append(*queryArguments, dateRange.To)
		argumentCounter++
	}

	// Step 4: Return the updated argument counter for subsequent parameters.
	return argumentCounter
}

// buildAssetOrderByClause constructs the ORDER BY clause based on the search request.
// This method creates appropriate sorting for search results, supporting multiple
// ordering strategies to meet different user needs. The ordering always includes
// a secondary sort by ID to ensure consistent pagination when primary sort values are identical.
//
// Supported ordering options:
// - SEARCH_ORDER_BY_RELEVANCE: Default relevance-based ordering (update_time DESC, name ASC, id ASC)
// - SEARCH_ORDER_BY_NAME: Alphabetical ordering by asset name
// - SEARCH_ORDER_BY_CREATE_TIME: Chronological ordering by creation time
// - SEARCH_ORDER_BY_UPDATE_TIME: Chronological ordering by last modification time
// - SEARCH_ORDER_BY_STATUS_CHANGED_TIME: Chronological ordering by status change time
// - SEARCH_ORDER_BY_LOCATION_UPDATE_TIME: Chronological ordering by location update time
//
// All orderings support both ascending and descending directions.
func (postgresAssetRepository *PostgresAssetRepository) buildAssetOrderByClause(orderByOption assets.SearchOrderBy, isAscending bool) string {
	// Step 1: Determine sort direction based on ascending flag.
	// Default is descending (newest/highest first) unless explicitly requested ascending.
	sortDirection := "DESC"
	if isAscending {
		sortDirection = "ASC"
	}

	// Step 2: Build ORDER BY clause based on the requested ordering option.
	// Each case includes ID as secondary sort for consistent pagination.
	switch orderByOption {
	case assets.SearchOrderBy_SEARCH_ORDER_BY_NAME:
		// Alphabetical ordering by asset name with ID as tiebreaker
		return fmt.Sprintf("ORDER BY a.name %s, a.id %s", sortDirection, sortDirection)

	case assets.SearchOrderBy_SEARCH_ORDER_BY_CREATE_TIME:
		// Chronological ordering by creation time with ID as tiebreaker
		return fmt.Sprintf("ORDER BY a.create_time %s, a.id %s", sortDirection, sortDirection)

	case assets.SearchOrderBy_SEARCH_ORDER_BY_UPDATE_TIME:
		// Chronological ordering by last modification time with ID as tiebreaker
		return fmt.Sprintf("ORDER BY a.update_time %s, a.id %s", sortDirection, sortDirection)

	case assets.SearchOrderBy_SEARCH_ORDER_BY_STATUS_CHANGED_TIME:
		// Chronological ordering by status change time with ID as tiebreaker
		return fmt.Sprintf("ORDER BY a.status_changed_time %s, a.id %s", sortDirection, sortDirection)

	case assets.SearchOrderBy_SEARCH_ORDER_BY_LOCATION_UPDATE_TIME:
		// Chronological ordering by location update time with ID as tiebreaker
		return fmt.Sprintf("ORDER BY a.location_update_time %s, a.id %s", sortDirection, sortDirection)

	case assets.SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE:
		fallthrough
	default:
		// Step 3: Default relevance-based ordering for optimal user experience.
		// Combines recency (update_time) with alphabetical ordering for intuitive results.
		// Recently updated assets appear first, then sorted alphabetically by name.
		return fmt.Sprintf("ORDER BY a.update_time %s, a.name %s, a.id %s", sortDirection, sortDirection, sortDirection)
	}
}

// scanAssetSearchRow scans a database row into an Asset object.
// This method converts a single row from the search query results into a complete
// Asset protobuf object, handling all type conversions, nullable fields, and
// timestamp formatting required for the API response.
//
// The method handles these data type conversions:
// - Integer enum values to protobuf enum types
// - PostgreSQL timestamps to ISO8601 strings
// - Nullable timestamps with proper empty string handling
// - Additional JSON info field assignment
//
// This scanning is optimized for search results and matches the field order
// from the SELECT clause in buildAssetSearchQuery.
func (postgresAssetRepository *PostgresAssetRepository) scanAssetSearchRow(databaseRow *sql.Rows) (*assets.Asset, error) {
	// Step 1: Initialize the Asset object and scanning variables.
	// Use appropriate types for database scanning before conversion to protobuf types.
	assetRecord := &assets.Asset{}
	var assetTypeValue, assetStatusValue int32
	var creationTimestamp, updateTimestamp time.Time
	var locationUpdateTimestamp, statusChangedTimestamp sql.NullTime
	var additionalInformation string

	// Step 2: Scan the database row into local variables.
	// The field order must match exactly with the SELECT clause from buildAssetSearchQuery.
	// Uses sql.NullTime for nullable timestamp fields to handle NULL values properly.
	scanError := databaseRow.Scan(
		&assetRecord.Id,            // Asset unique identifier
		&assetRecord.OrgId,         // Organization ID for multi-tenancy
		&assetRecord.CognitoJwtSub, // Cognito JWT subject for authentication
		&assetRecord.Name,          // Asset display name
		&assetTypeValue,            // Asset type as integer (converted to enum below)
		&assetStatusValue,          // Asset status as integer (converted to enum below)
		&assetRecord.Latitude,      // Geographic latitude coordinate
		&assetRecord.Longitude,     // Geographic longitude coordinate
		&locationUpdateTimestamp,   // When location was last updated (nullable)
		&assetRecord.ContactNo,     // Contact phone number
		&assetRecord.ContactEmail,  // Contact email address
		&creationTimestamp,         // When asset was created (required)
		&updateTimestamp,           // When asset was last modified (required)
		&assetRecord.ResourceType,  // Resource type identifier
		&additionalInformation,     // Additional info as JSON string
		&statusChangedTimestamp,    // When status was last changed (nullable)
	)
	if scanError != nil {
		return nil, scanError
	}

	// Step 3: Convert integer enum values to protobuf enum types.
	// Database stores enums as integers for efficiency and consistency.
	assetRecord.Type = assets.AssetType(assetTypeValue)
	assetRecord.Status = assets.AssetStatus(assetStatusValue)

	// Step 4: Convert required timestamps to ISO8601 string format.
	// These timestamps are always present and required for proper API responses.
	assetRecord.CreateTime = commonUtils.TimeToISO8601String(creationTimestamp)
	assetRecord.UpdateTime = commonUtils.TimeToISO8601String(updateTimestamp)

	// Step 5: Handle nullable location update timestamp.
	// Convert to ISO8601 string if present, empty string if NULL.
	if locationUpdateTimestamp.Valid {
		assetRecord.LocationUpdateTime = commonUtils.TimeToISO8601String(locationUpdateTimestamp.Time)
	} else {
		assetRecord.LocationUpdateTime = ""
	}

	// Step 6: Handle nullable status changed timestamp.
	// Convert to ISO8601 string if present, empty string if NULL.
	if statusChangedTimestamp.Valid {
		assetRecord.StatusChangedTime = commonUtils.TimeToISO8601String(statusChangedTimestamp.Time)
	} else {
		assetRecord.StatusChangedTime = ""
	}

	// Step 7: Assign additional info JSON field.
	// This field contains extra asset metadata as a JSON string.
	assetRecord.AdditionalInfoJson = additionalInformation

	// Step 8: Return the completely populated Asset object.
	return assetRecord, nil
}

// getAssetSearchTotalCount retrieves the total count of assets matching the search criteria.
// This method executes a separate COUNT(*) query using the same WHERE conditions as the
// main search query, but without LIMIT/OFFSET to get the total number of matching results.
// This count is essential for pagination metadata, allowing clients to calculate total
// pages and display accurate pagination controls.
//
// The method builds a fresh COUNT query using the same filtering logic as the main search.
func (postgresAssetRepository *PostgresAssetRepository) getAssetSearchTotalCount(
	requestContext context.Context,
	databaseTransaction *sql.Tx,
	whereConditionsList []string,
	queryArguments []interface{},
) (int, error) {
	// Step 1: Build a simple COUNT query from scratch.
	// This is more reliable than trying to transform the complex SELECT query.
	countQuery := "SELECT COUNT(*) FROM assets a WHERE a.org_id = $1"

	// Step 2: Add all WHERE conditions to the COUNT query.
	// This ensures the count reflects the same filtering criteria as the main search.
	if len(whereConditionsList) > 0 {
		countQuery += " AND " + strings.Join(whereConditionsList, " AND ")
	}

	// Step 3: Execute the COUNT query and scan the result.
	// Use the same transaction and arguments (excluding LIMIT/OFFSET) for consistency.
	var totalCount int
	countRow := databaseTransaction.QueryRowContext(requestContext, countQuery, queryArguments...)
	if scanError := countRow.Scan(&totalCount); scanError != nil {
		return 0, fmt.Errorf("failed to get total count: %w", scanError)
	}

	// Step 4: Return the total count for pagination metadata.
	return totalCount, nil
}

// generateAssetSearchHighlights creates highlight information for search results.
// This method analyzes the search results and search terms to generate highlighted
// text fragments showing users exactly where their search terms were found.
// Highlights enhance the search experience by providing visual feedback about matches.
//
// The highlighting process:
// 1. Examines each returned asset for search term matches
// 2. Creates text fragments with context around matched terms
// 3. Identifies which fields contained matches
// 4. Deduplicates fragments to avoid repetition
// 5. Returns a map of asset ID to highlight information
//
// Highlighting supports all searchable fields: id, name, contact_no, contact_email.
func (postgresAssetRepository *PostgresAssetRepository) generateAssetSearchHighlights(
	foundAssets []*assets.Asset,
	searchTermsMap map[string][]string,
) map[string]*assets.HighlightResult {
	// Step 1: Initialize the highlights map for results.
	// This map will contain highlights keyed by asset ID for efficient lookup.
	highlights := make(map[string]*assets.HighlightResult)

	// Step 2: Process each found asset for potential highlights.
	// Only assets with matching search terms will have highlight entries.
	for _, assetRecord := range foundAssets {
		var allFragments []string
		var highlightedFields []string

		// Step 3: Create field-to-value mapping for efficient field access.
		// This allows us to check field values without repetitive conditional logic.
		fieldValueMap := map[string]string{
			FieldID:           assetRecord.Id,
			FieldName:         assetRecord.Name,
			FieldContactNo:    assetRecord.ContactNo,
			FieldContactEmail: assetRecord.ContactEmail,
		}

		// Step 4: Check each searchable field against all search terms.
		// This creates highlights for every field/term combination that matches.
		for fieldName, searchTerms := range searchTermsMap {
			fieldValue, exists := fieldValueMap[fieldName]
			if !exists || fieldValue == "" {
				continue // Skip empty or non-existent fields
			}

			// Step 5: Check each search term against the current field value.
			// Multiple terms can match the same field, creating multiple fragments.
			for _, searchTerm := range searchTerms {
				if postgresAssetRepository.containsTermIgnoreCase(fieldValue, searchTerm) {
					// Step 6: Create a highlighted fragment for the match.
					// This includes context around the matched term for readability.
					fragment := postgresAssetRepository.createHighlightFragment(fieldValue, searchTerm)
					if fragment != "" {
						allFragments = append(allFragments, fragment)
						// Track which fields had matches for summary information
						if !postgresAssetRepository.containsString(highlightedFields, fieldName) {
							highlightedFields = append(highlightedFields, fieldName)
						}
					}
				}
			}
		}

		// Step 7: Create highlight result if any matches were found.
		// Only assets with actual matches get highlight entries.
		if len(allFragments) > 0 {
			highlights[assetRecord.Id] = &assets.HighlightResult{
				Field:     strings.Join(highlightedFields, ", "),                      // Summary of matched fields
				Fragments: postgresAssetRepository.deduplicateFragments(allFragments), // Unique text fragments
			}
		}
	}

	// Step 8: Return the complete highlights map.
	return highlights
}

// containsTermIgnoreCase checks if text contains the search term (case-insensitive).
// This method provides case-insensitive substring matching for search term detection.
// It's used in the highlighting system to determine if a field value contains a search term.
//
// The method converts both the text content and search term to lowercase before
// performing the substring check, ensuring consistent matching regardless of case.
func (postgresAssetRepository *PostgresAssetRepository) containsTermIgnoreCase(textContent, searchTerm string) bool {
	return strings.Contains(strings.ToLower(textContent), strings.ToLower(searchTerm))
}

// createHighlightFragment creates a highlighted fragment around the matched term.
// This method generates a text snippet with context around a search term match,
// providing users with readable context for where their search terms were found.
//
// The fragment creation process:
// 1. Locates the search term within the text (case-insensitive)
// 2. Extracts surrounding context (30 characters before and after)
// 3. Adds ellipsis ("…") if the fragment doesn't include the full text
// 4. Returns the complete fragment for display in search results
//
// This helps users understand the context of matches without displaying entire field values.
func (postgresAssetRepository *PostgresAssetRepository) createHighlightFragment(textContent, searchTerm string) string {
	// Step 1: Perform case-insensitive search to locate the term.
	// Convert both text and term to lowercase for consistent matching.
	lowerCaseText := strings.ToLower(textContent)
	lowerCaseTerm := strings.ToLower(searchTerm)

	termIndex := strings.Index(lowerCaseText, lowerCaseTerm)
	if termIndex == -1 {
		return "" // Term not found, no fragment to create
	}

	// Step 2: Calculate fragment boundaries with context around the match.
	// Provide 30 characters of context before and after the match for readability.
	contextLength := 30
	fragmentStartIndex := postgresAssetRepository.maxInt(0, termIndex-contextLength)
	fragmentEndIndex := postgresAssetRepository.minInt(len(textContent), termIndex+len(searchTerm)+contextLength)

	// Step 3: Extract the fragment from the original text (preserving case).
	// Use original text rather than lowercase to maintain proper capitalization.
	highlightFragment := textContent[fragmentStartIndex:fragmentEndIndex]

	// Step 4: Add ellipsis indicators if we truncated content.
	// This shows users that there's more content beyond the displayed fragment.
	if fragmentStartIndex > 0 {
		highlightFragment = "…" + highlightFragment
	}
	if fragmentEndIndex < len(textContent) {
		highlightFragment += "…"
	}

	// Step 5: Return the complete fragment ready for display.
	return highlightFragment
}

// deduplicateFragments removes duplicate fragments from the list.
// This method ensures that identical text fragments aren't shown multiple times
// in search highlights, providing a cleaner user experience.
//
// The deduplication uses a map to track seen fragments efficiently,
// preserving the original order of unique fragments while removing duplicates.
func (postgresAssetRepository *PostgresAssetRepository) deduplicateFragments(fragmentsList []string) []string {
	// Step 1: Initialize tracking structures for deduplication.
	// Use a map for fast duplicate detection and slice for ordered results.
	seenFragments := make(map[string]bool)
	var uniqueFragments []string

	// Step 2: Process each fragment, keeping only unique ones.
	// Maintain the original order while removing duplicates.
	for _, fragment := range fragmentsList {
		if !seenFragments[fragment] {
			seenFragments[fragment] = true
			uniqueFragments = append(uniqueFragments, fragment)
		}
	}

	// Step 3: Return the deduplicated list maintaining original order.
	return uniqueFragments
}

// containsString checks if a string slice contains a specific string.
// This utility method provides efficient string slice membership testing,
// used in the highlighting system to track which fields have been processed.
//
// The method uses linear search which is efficient for small slices
// (like the list of highlighted fields in search results).
func (postgresAssetRepository *PostgresAssetRepository) containsString(stringSlice []string, targetItem string) bool {
	// Step 1: Iterate through the slice looking for the target string.
	// Linear search is appropriate for small slices common in this use case.
	for _, stringItem := range stringSlice {
		if stringItem == targetItem {
			return true
		}
	}
	// Step 2: Return false if target not found in slice.
	return false
}

// minInt returns the minimum of two integers.
// This utility method provides safe integer minimum calculation for fragment boundaries.
// Used in highlight fragment creation to ensure array bounds are respected.
func (postgresAssetRepository *PostgresAssetRepository) minInt(firstValue, secondValue int) int {
	if firstValue < secondValue {
		return firstValue
	}
	return secondValue
}

// maxInt returns the maximum of two integers.
// This utility method provides safe integer maximum calculation for fragment boundaries.
// Used in highlight fragment creation to ensure array bounds are respected.
func (postgresAssetRepository *PostgresAssetRepository) maxInt(firstValue, secondValue int) int {
	if firstValue > secondValue {
		return firstValue
	}
	return secondValue
}

// BatchGetAssets retrieves multiple assets by their IDs in a single operation.
// This method optimizes database access by fetching multiple assets at once,
// reducing the number of database round trips and improving performance.
//
// The method uses an IN clause with parameterized queries for security and efficiency.
// Assets are returned in the same order as the input IDs when possible.
// If any asset is not found, it will be excluded from the results.
//
// Parameters:
//   - ctx: Context for cancellation and timeouts
//   - transaction: Database transaction for consistency
//   - assetIDs: List of asset IDs to retrieve
//
// Returns:
//   - []*assets.Asset: Array of found assets (may be fewer than requested)
//   - error: Database or query execution errors
func (postgresAssetRepository *PostgresAssetRepository) BatchGetAssets(ctx context.Context, transaction *sql.Tx, assetIDs []string) ([]*assets.Asset, error) {
	if len(assetIDs) == 0 {
		return []*assets.Asset{}, nil // Return empty slice for no IDs
	}

	return database.WithSession(postgresAssetRepository.database, ctx, transaction, func(tx *sql.Tx) ([]*assets.Asset, error) {
		// Build IN clause with parameterized queries
		placeholders := make([]string, len(assetIDs))
		queryArguments := make([]interface{}, len(assetIDs))
		for i, assetID := range assetIDs {
			placeholders[i] = "$" + strconv.Itoa(i+1)
			queryArguments[i] = assetID
		}

		// Build the batch query using fmt.Sprintf to avoid gosec warning
		selectQuery := fmt.Sprintf(`
			SELECT id, org_id, cognito_jwt_sub, name, type, status, latitude, longitude, location_update_time, 
			       contact_no, contact_email, create_time, update_time, resource_type, 
			       additional_info_json, status_changed_time
			FROM assets
			WHERE id IN (%s)`, strings.Join(placeholders, ", ")) // #nosec G201 -- placeholders are safely generated parameter strings ($1, $2, etc.)

		// Execute the batch query
		queryRows, queryError := tx.QueryContext(ctx, selectQuery, queryArguments...)
		if queryError != nil {
			return nil, queryError
		}
		defer queryRows.Close()

		// Process the returned rows
		var foundAssets []*assets.Asset
		for queryRows.Next() {
			assetRecord := &assets.Asset{}
			var assetTypeValue, assetStatusValue int32
			var creationTimestamp, updateTimestamp time.Time
			var locationUpdateTimestamp sql.NullTime
			var additionalInformation string
			var statusChangedTimestamp sql.NullTime

			scanError := queryRows.Scan(
				&assetRecord.Id,
				&assetRecord.OrgId,
				&assetRecord.CognitoJwtSub,
				&assetRecord.Name,
				&assetTypeValue,
				&assetStatusValue,
				&assetRecord.Latitude,
				&assetRecord.Longitude,
				&locationUpdateTimestamp,
				&assetRecord.ContactNo,
				&assetRecord.ContactEmail,
				&creationTimestamp,
				&updateTimestamp,
				&assetRecord.ResourceType,
				&additionalInformation,
				&statusChangedTimestamp,
			)
			if scanError != nil {
				return nil, scanError
			}

			// Convert database types to protobuf types
			assetRecord.Type = assets.AssetType(assetTypeValue)
			assetRecord.Status = assets.AssetStatus(assetStatusValue)
			assetRecord.CreateTime = commonUtils.TimeToISO8601String(creationTimestamp)
			assetRecord.UpdateTime = commonUtils.TimeToISO8601String(updateTimestamp)
			assetRecord.AdditionalInfoJson = additionalInformation

			// Handle nullable timestamps
			if locationUpdateTimestamp.Valid {
				assetRecord.LocationUpdateTime = commonUtils.TimeToISO8601String(locationUpdateTimestamp.Time)
			} else {
				assetRecord.LocationUpdateTime = ""
			}

			if statusChangedTimestamp.Valid {
				assetRecord.StatusChangedTime = commonUtils.TimeToISO8601String(statusChangedTimestamp.Time)
			} else {
				assetRecord.StatusChangedTime = ""
			}

			foundAssets = append(foundAssets, assetRecord)
		}

		// Check for any errors during iteration
		if rowErr := queryRows.Err(); rowErr != nil {
			return nil, rowErr
		}

		return foundAssets, nil
	})
}

// safeIntToInt32 safely converts int to int32 with bounds checking.
// This method prevents integer overflow when converting between int and int32 types,
// which is essential for safe protobuf field assignment and database interactions.
//
// The method enforces these bounds:
// - Values above int32 max (2,147,483,647) are clamped to max
// - Negative values are clamped to 0 for count fields
// - Values within range are safely converted
//
// This ensures that large result counts don't cause overflow issues in API responses.
func (postgresAssetRepository *PostgresAssetRepository) safeIntToInt32(inputValue int) int32 {
	// Step 1: Check upper bound to prevent overflow.
	// int32 maximum value is 2,147,483,647
	if inputValue > 2147483647 {
		return 2147483647
	}

	// Step 2: Check lower bound for count fields.
	// Negative counts don't make sense, clamp to 0
	if inputValue < 0 {
		return 0
	}

	// Step 3: Perform safe conversion within validated bounds.
	// This conversion is now guaranteed to be safe from overflow.
	convertedResult := int32(inputValue) // nolint:gosec // Safe conversion after bounds check
	return convertedResult
}
