package repository

import (
	"context"
	"database/sql"
	"errors"

	assets "proto/hero/assets/v2"

	_ "github.com/lib/pq"
)

// ErrAssetNotFound is returned when an asset cannot be found.
var ErrAssetNotFound = errors.New("asset not found")

const FixedResourceTypeAsset = "ASSET"

type PaginatedAssets struct {
	Assets        []*assets.Asset
	NextPageToken string
}

type ZelloChannel struct {
	ID             string
	OrgID          string
	ZelloChannelID string
	DisplayName    string
}

// AssetRepository defines the operations for managing assets.
type AssetRepository interface {
	// CreateAsset stores a new asset.
	CreateAsset(ctx context.Context, transaction *sql.Tx, asset *assets.Asset) error
	// GetAsset returns an asset by its ID.
	GetAsset(ctx context.Context, transaction *sql.Tx, assetID string) (*assets.Asset, error)
	// GetAssetByCognitoSub returns an asset by its Cognito JWT sub.
	GetAssetByCognitoSub(ctx context.Context, transaction *sql.Tx, cognitoSub string) (*assets.Asset, error)
	// ListAssetsByPhoneNumber returns all assets associated with a phone number.
	ListAssetsByPhoneNumber(ctx context.Context, transaction *sql.Tx, phoneNumber string) ([]*assets.Asset, error)
	// ListAssets returns a paginated list of assets.
	ListAssets(ctx context.Context, transaction *sql.Tx, pageSize int, pageToken string, assetType assets.AssetType, assetStatus assets.AssetStatus, orderBy string) (*PaginatedAssets, error)
	// DeleteAsset physically deletes an asset from the store.
	DeleteAsset(ctx context.Context, transaction *sql.Tx, assetID string) error
	// UpdateAsset updates the asset fields.
	UpdateAsset(ctx context.Context, transaction *sql.Tx, asset *assets.Asset) (*assets.Asset, error)

	// SearchAssets performs advanced search on assets with text matching, filtering, and geographic queries.
	SearchAssets(ctx context.Context, transaction *sql.Tx, searchRequest *assets.SearchAssetsRequest) (*assets.SearchAssetsResponse, error)

	// BatchGetAssets retrieves multiple assets by their IDs in a single operation.
	BatchGetAssets(ctx context.Context, transaction *sql.Tx, assetIDs []string) ([]*assets.Asset, error)

	GetZelloChannels(ctx context.Context, transaction *sql.Tx) ([]*assets.ZelloChannel, error)

	CreateZelloCreds(ctx context.Context, transaction *sql.Tx, zelloCreds *assets.ZelloCreds) error
	GetZelloCreds(ctx context.Context, transaction *sql.Tx, assetID string) (*assets.ZelloCreds, error)
	UpdateZelloCreds(ctx context.Context, transaction *sql.Tx, zelloCreds *assets.ZelloCreds) error
	DeleteZelloCreds(ctx context.Context, transaction *sql.Tx, assetID string) error

	UpdateAdditionalInfoJSON(ctx context.Context, transaction *sql.Tx, assetID string, additionalInfoJSON string) (string, error)
}

// NewAssetRepository returns an AssetRepository based on the provided configuration.
func NewAssetRepository(postgresDB *sql.DB) (AssetRepository, *sql.DB, error) {
	if postgresDB == nil {
		return nil, nil, errors.New("database is nil: cannot initialize AssetRepository")
	}
	return NewPostgresAssetRepository(postgresDB), postgresDB, nil

}
