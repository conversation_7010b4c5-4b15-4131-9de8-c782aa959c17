#!/bin/bash

# Simple script to run report tests with token from token.txt
# Usage: ./run_tests.sh [sanity|search|search-fast|performance|scalability|all|populate|cleanup|hydrated] [nocache]
# Default (no arguments): runs all tests

# Default test type
TEST_TYPE="all"
NO_CACHE=""

# Parse command line arguments
if [ "$1" == "sanity" ]; then
  TEST_TYPE="sanity"
  echo "Running sanity tests..."
elif [ "$1" == "search" ]; then
  TEST_TYPE="search"
  echo "Running search tests (including performance)..."
elif [ "$1" == "search-fast" ]; then
  TEST_TYPE="search-fast"
  echo "Running 4 comprehensive search tests (excluding performance)..."
elif [ "$1" == "search-performance" ]; then
  TEST_TYPE="search-performance"
  echo "Running search performance tests..."
elif [ "$1" == "performance" ]; then
  TEST_TYPE="performance"
  echo "Running basic performance tests..."
elif [ "$1" == "scalability" ]; then
  TEST_TYPE="scalability"
  echo "Running extreme scalability stress tests..."
elif [ "$1" == "populate" ]; then
  TEST_TYPE="populate"
  echo "Running populate script to create test reports..."
elif [ "$1" == "cleanup" ]; then
  TEST_TYPE="cleanup"
  echo "Running cleanup script to delete test reports..."
elif [ "$1" == "hydrated" ]; then
  TEST_TYPE="hydrated"
  echo "Running fully hydrated report generation test..."
elif [ "$1" == "all" ] || [ "$1" == "" ]; then
  TEST_TYPE="all"
  echo "Running all tests..."
elif [ "$1" == "help" ] || [ "$1" == "-h" ] || [ "$1" == "--help" ]; then
  echo "Reports Test Runner"
  echo "=================="
  echo ""
  echo "Usage: $0 [test-type] [options]"
  echo ""
  echo "Test Types:"
  echo "  sanity            Run basic sanity tests (TestSanity_*)"
  echo "  performance       Run basic performance tests (~30 sec)"
  echo "  search            Run all search tests including performance (~2 min)"
  echo "  search-fast       Run 4 comprehensive search tests (~15 sec)"
  echo "  search-performance Run only search performance tests (~90 sec)"
  echo "  scalability       Run scalability stress tests with 100,000+ reports (~45-60 min)"
  echo "  populate     Create test data in database"
  echo "  cleanup      Remove all test data from database"
  echo "  hydrated     Create single fully hydrated report with all connections"
  echo "  all          Run sanity + performance + search tests (default)"
  echo ""
  echo "Options:"
  echo "  nocache      Bypass Go test cache (-count=1)"
  echo ""
  echo "Examples:"
  echo "  $0                         # Run all tests"
  echo "  $0 performance             # Quick performance validation"
  echo "  $0 search-fast             # Quick search validation"
  echo "  $0 search-performance      # Database optimization validation"
  echo "  $0 scalability             # High-load scalability testing"
  echo "  $0 search nocache          # Search tests bypassing cache"
  echo ""
  echo "Prerequisites:"
  echo "  - Token file: token.txt (contains COGNITO_ACCESS_TOKEN)"
  echo "  - Working directory: services/workflow/"
  echo ""
  echo "Scalability Test Warning:"
  echo "  - Creates 100,000+ reports, assets, situations, and relations"
  echo "  - Takes 45-60 minutes to complete"
  echo "  - Uses significant database resources"
  echo "  - Automatically cleans up test data"
  echo "  - Should not be run on production systems"
  echo ""
  exit 0
else
  echo "Unknown test type: $1"
  echo "Usage: $0 [sanity|performance|search|search-fast|search-performance|scalability|all|populate|cleanup|hydrated] [nocache]"
  echo "Use '$0 help' for detailed information."
  exit 1
fi

# Check for nocache argument
if [ "$2" == "nocache" ]; then
  NO_CACHE="-count=1"
  echo "Bypassing test cache..."
fi

# Get directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
TOKEN_FILE="$SCRIPT_DIR/token.txt"

# Check if token file exists
if [ ! -f "$TOKEN_FILE" ]; then
  echo "Error: Token file '$TOKEN_FILE' not found."
  exit 1
fi

# Read the token from the file
TOKEN=$(cat "$TOKEN_FILE" | tr -d '\n\r ')

# Verify token is not empty
if [ -z "$TOKEN" ]; then
  echo "Error: No token found in '$TOKEN_FILE'."
  exit 1
fi

# Export the token as environment variable
export COGNITO_ACCESS_TOKEN="$TOKEN"

# Change to the workflow directory
cd "$SCRIPT_DIR/../../"

# Run the tests
case "$TEST_TYPE" in
  sanity)
    go test -v -run "^TestSanity_" $NO_CACHE ./test/reports
    ;;
  performance)
    go test -v -run "^TestReports_BasicPerformance$" $NO_CACHE ./test/reports -timeout=600s
    ;;
  search)
    go test -v -run "^TestSearch_|^TestSearchReports_" $NO_CACHE ./test/reports
    ;;
  search-fast)
    # Runs all search tests except performance: TestSearch_Reports, TestSearch_ValidationEnhancements_*, 
    # TestSearch_ComprehensiveParameterValidation, TestSearch_MissingParametersAndEdgeCases
    go test -v -run "^TestSearch_(Reports$|ValidationEnhancements_|ComprehensiveParameterValidation$|MissingParametersAndEdgeCases$)" $NO_CACHE ./test/reports
    ;;
  search-performance)
    go test -v -run "^TestSearch_.*Performance" $NO_CACHE ./test/reports -timeout=600s
    ;;
  scalability)
    echo ""
    echo "🔥🔥🔥 WARNING: EXTREME SCALABILITY STRESS TEST 🔥🔥🔥"
    echo "This test will create 100,000+ reports with full content and relations"
    echo "Expected runtime: 45-60 minutes"
    echo "This will heavily load your database and system resources"
    echo "❌ DO NOT run this on production systems!"
    echo ""
    echo "Press Ctrl+C within 10 seconds to cancel..."
    for i in {10..1}; do
      echo "Starting in $i seconds..."
      sleep 1
    done
    echo "🚀 Starting extreme scalability test..."
    ENABLE_STRESS_TESTS=true go test -v -timeout=60m -run "^TestReportsSearchScalability$" $NO_CACHE ./test/reports
    SCALABILITY_RESULT=$?
    echo ""
    if [ $SCALABILITY_RESULT -eq 0 ]; then
      echo "✅ Scalability stress test completed successfully!"
    else
      echo "❌ Scalability stress test failed!"
    fi
    exit $SCALABILITY_RESULT
    ;;
  populate)
    go test -v -run "^TestPopulateReports$" $NO_CACHE ./test/reports
    ;;
  cleanup)
    go test -v -run "^TestCleanupAllReports$" $NO_CACHE ./test/reports
    ;;
  hydrated)
    go test -v -run "^TestGenerateFullyHydratedReport$" $NO_CACHE ./test/reports
    ;;
  all)
    echo "Running sanity tests..."
    go test -v -run "^TestSanity_" $NO_CACHE ./test/reports
    SANITY_RESULT=$?
    echo ""
    echo "Running performance tests..."
    go test -v -run "^TestReports_BasicPerformance$" $NO_CACHE ./test/reports -timeout=600s
    PERFORMANCE_RESULT=$?
    echo ""
    echo "Running search tests..."
    go test -v -run "^TestSearch_|^TestSearchReports_" $NO_CACHE ./test/reports
    SEARCH_RESULT=$?
    
    if [ $SANITY_RESULT -eq 0 ] && [ $PERFORMANCE_RESULT -eq 0 ] && [ $SEARCH_RESULT -eq 0 ]; then
      echo "All tests passed!"
      exit 0
    else
      echo "Some tests failed!"
      exit 1
    fi
    ;;
esac 