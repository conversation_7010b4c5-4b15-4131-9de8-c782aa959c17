package main

import (
	database "common/database"
	"common/middleware"
	"database/sql"
	"log"
	"net/http"
	"os"
	"time"

	"workflow/internal/assets"
	"workflow/internal/cases"
	"workflow/internal/entity"
	"workflow/internal/etl"
	"workflow/internal/orders"
	"workflow/internal/reports"
	"workflow/internal/situations"

	"github.com/getsentry/sentry-go"

	assetRepository "workflow/internal/assets/data"
	caseRepository "workflow/internal/cases/data"
	entityRepository "workflow/internal/entity/data"
	etlRepository "workflow/internal/etl/data"
	orderRepository "workflow/internal/orders/data"
	reportRepository "workflow/internal/reports/data"
	situationRepository "workflow/internal/situations/data"
)

func main() {
	// Initialize Sentry for error tracking and performance monitoring
	sentrySampleRate := 1.0 // Default to 100% for development
	if env := os.Getenv("ENVIRONMENT"); env == "production" {
		sentrySampleRate = 0.1 // 10% sampling for production
	}

	sentryOptions := sentry.ClientOptions{
		Dsn:              os.Getenv("SENTRY_DSN"),
		Environment:      os.Getenv("ENVIRONMENT"),
		ServerName:       "workflow-service",
		EnableTracing:    true,
		TracesSampleRate: sentrySampleRate,
		SendDefaultPII:   false, // Security: Don't send PII
	}
	// Optional release tracking for deployment correlation
	if appVersion := os.Getenv("APP_VERSION"); appVersion != "" {
		sentryOptions.Release = appVersion
	}
	err := sentry.Init(sentryOptions)
	if err != nil {
		log.Printf("Sentry initialization failed: %v", err)
	}

	mux := http.NewServeMux()

	// Initialize all DB
	repositoryType := os.Getenv("REPO_TYPE")

	var postGresDB *sql.DB = nil

	if repositoryType == "postgres" {
		databaseURL, err := database.CreateDBURL()
		if err != nil {
			sentry.CaptureException(err)
			sentry.Flush(2 * time.Second)
			log.Fatalf("failed to get postgres db url: %v", err)
		}
		var openError error
		postGresDB, openError = sql.Open("postgres", databaseURL)
		if openError != nil {
			sentry.CaptureException(openError)
			sentry.Flush(2 * time.Second)
			log.Fatalf("failed to open postgres db: %v", openError)
		}

		// Configure connection pool for optimal performance
		// Max open connections - limit concurrent connections to prevent resource exhaustion
		postGresDB.SetMaxOpenConns(100)

		// Max idle connections - keep connections alive for reuse
		postGresDB.SetMaxIdleConns(35)

		// Connection max lifetime - force connection refresh to prevent stale connections
		postGresDB.SetConnMaxLifetime(5 * time.Minute)
	}

	// Initialize Asset Repository
	assetRepo, assetDB, err := assetRepository.NewAssetRepository(postGresDB)
	if err != nil {
		sentry.CaptureException(err)
		sentry.Flush(2 * time.Second)
		log.Fatal("Failed to initialize asset repository:", err)
	}

	// Initialize Situation Repository
	situationRepo, situationDB, err := situationRepository.NewSituationRepository(postGresDB)
	if err != nil {
		sentry.CaptureException(err)
		sentry.Flush(2 * time.Second)
		log.Fatal("Failed to initialize situation repository:", err)
	}

	// Initialize Order Repository
	orderRepo, orderDB, err := orderRepository.NewOrderRepository(postGresDB)
	if err != nil {
		sentry.CaptureException(err)
		sentry.Flush(2 * time.Second)
		log.Fatal("Failed to initialize order repository:", err)
	}

	// Initialize Entity Repository
	entityRepo, entityDB, err := entityRepository.NewEntityRepository(postGresDB)
	if err != nil {
		sentry.CaptureException(err)
		sentry.Flush(2 * time.Second)
		log.Fatal("Failed to initialize entity repository:", err)
	}

	// Initialize Report Repository
	reportRepo, reportDB, err := reportRepository.NewReportRepository(postGresDB)
	if err != nil {
		sentry.CaptureException(err)
		sentry.Flush(2 * time.Second)
		log.Fatal("Failed to initialize report repository:", err)
	}

	// Initialize Case Repository
	caseRepo, caseDB, err := caseRepository.NewCaseRepository(postGresDB)
	if err != nil {
		sentry.CaptureException(err)
		sentry.Flush(2 * time.Second)
		log.Fatal("Failed to initialize case repository:", err)
	}

	// Initialize ETL Repository
	etlRepo, etlDB, err := etlRepository.NewETLRepository(postGresDB)
	if err != nil {
		log.Fatal("Failed to initialize ETL repository:", err)
	}

	// Register all endpoints.
	assets.RegisterRoutes(mux, assetDB, assetRepo, situationRepo, orderRepo)
	situations.RegisterRoutes(mux, situationDB, assetRepo, situationRepo, orderRepo)
	orders.RegisterRoutes(mux, orderDB, assetRepo, situationRepo, orderRepo, reportRepo)
	entity.RegisterRoutes(mux, entityDB, entityRepo)
	reports.RegisterRoutes(mux, reportDB, assetRepo, reportRepo, orderRepo)
	cases.RegisterRoutes(mux, caseDB, assetRepo, entityRepo, orderRepo, reportRepo, situationRepo, caseRepo)
	etl.RegisterRoutes(mux, etlDB, etlRepo, reportRepo, entityRepo, situationRepo, assetRepo)

	// Additional endpoints.
	// Create a new mux for health endpoints that bypasses auth
	healthMux := middleware.NewHealthMux(middleware.HealthMuxConfig{
		ServiceNames: []string{
			"hero.assets.v2.AssetRegistryService",
			"hero.orders.v2.OrderService",
			"hero.situations.v2.SituationService",
			"hero.entity.v1.EntityService",
			"hero.reports.v2.ReportService",
			"hero.cases.v1.CaseService",
			"hero.etl.v1.ETLService",
		},
		HealthResponse: "YES HOW CAN I HELP YOU",
	})

	// Create the server with health endpoints
	skipPerms := os.Getenv("SKIP_PERMISSIONS_CHECK") == "true"
	srv := middleware.NewServerWithHealth(
		":8080",
		mux,
		healthMux,
		!skipPerms,
	)

	log.Println("Workflow server listening on http://localhost:8080")
	if err := srv.ListenAndServe(); err != nil {
		sentry.CaptureException(err)
		sentry.Flush(2 * time.Second)
		log.Fatalf("Failed to serve: %v", err)
	}
	// Ensure events are sent before normal shutdown
	sentry.Flush(2 * time.Second)
}
