t=2025-07-09T16:29:04-0700 lvl=info msg="no configuration paths supplied"
t=2025-07-09T16:29:04-0700 lvl=info msg="using configuration at default config path" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml"
t=2025-07-09T16:29:04-0700 lvl=info msg="open config file" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml" err=nil
t=2025-07-09T16:29:04-0700 lvl=info msg="starting web service" obj=web addr=127.0.0.1:4040 allow_hosts=[]
t=2025-07-09T16:29:04-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T16:29:04-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T16:29:04-0700 lvl=info msg="started tunnel" obj=tunnels name=command_line addr=http://localhost:9084 url=https://5fb62915a1df.ngrok-free.app
t=2025-07-09T16:29:05-0700 lvl=info msg="update available" obj=updater
t=2025-07-09T16:29:05-0700 lvl=info msg="join connections" obj=join id=65ef46698bb3 l=[::1]:9084 r=**************:53883
t=2025-07-09T16:33:37-0700 lvl=info msg="join connections" obj=join id=4572132cf7b1 l=[::1]:9084 r=************:35716
t=2025-07-09T16:33:37-0700 lvl=info msg="join connections" obj=join id=5a876083cde9 l=[::1]:9084 r=*************:52392
t=2025-07-09T16:33:44-0700 lvl=info msg="join connections" obj=join id=329821327df8 l=[::1]:9084 r=*************:61262
t=2025-07-09T16:33:44-0700 lvl=info msg="join connections" obj=join id=402d62329820 l=[::1]:9084 r=*************:23256
t=2025-07-09T16:34:41-0700 lvl=info msg="join connections" obj=join id=96dbe53903b1 l=[::1]:9084 r=*************:47524
t=2025-07-09T16:34:41-0700 lvl=info msg="join connections" obj=join id=0a28c1e31a1f l=[::1]:9084 r=*************:64302
t=2025-07-09T16:34:43-0700 lvl=info msg="join connections" obj=join id=3d65f8e8b712 l=[::1]:9084 r=18.207.195.18:32214
t=2025-07-09T16:34:43-0700 lvl=info msg="join connections" obj=join id=e424053b7ef1 l=[::1]:9084 r=18.209.18.252:62280
t=2025-07-09T16:34:47-0700 lvl=info msg="join connections" obj=join id=49e623f85458 l=[::1]:9084 r=13.217.207.176:39464
t=2025-07-09T16:34:47-0700 lvl=info msg="join connections" obj=join id=f3e1c914b101 l=[::1]:9084 r=13.218.206.69:64838
t=2025-07-09T16:42:24-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-09T16:42:24-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T16:42:24-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T16:42:36-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=eac68ba41b51 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T16:52:04-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:60019->52.53.75.151:443: read: connection reset by peer"
t=2025-07-09T16:52:05-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T16:52:05-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T16:52:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=48004131b4b2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T17:00:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=def9cf4bda2c clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T17:00:19-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T17:00:19-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T17:00:19-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T17:53:44-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=22ce53109c6f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T17:53:44-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T17:53:44-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T17:53:44-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T17:54:26-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a6df9a4d545e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T17:54:26-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T17:54:26-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp [2600:1f1c:d8:5f01::6e74:5]:443: connect: no route to host"
t=2025-07-09T17:54:26-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp [2600:1f1c:d8:5f01::6e74:5]:443: connect: no route to host"
t=2025-07-09T17:54:27-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp [2600:1f1c:d8:5f01::6e74:5]:443: connect: no route to host"
t=2025-07-09T18:26:22-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-09T18:26:33-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-09T18:26:45-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-09T18:28:29-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T18:28:29-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T18:46:39-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2d10c98afcf9 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T18:46:39-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T18:46:39-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T18:46:39-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T19:17:46-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:49892->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-09T19:17:47-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T19:17:47-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T19:34:16-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:50013->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-09T19:34:16-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T19:34:16-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T19:34:20-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=21e1a789e683 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T19:49:54-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:50128->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-09T19:49:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T19:49:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T19:51:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2f0d430ddd0b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T20:07:55-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:50228->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-09T20:07:56-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T20:07:56-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T20:25:30-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e23e008abe1e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T20:41:46-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:50375->52.53.75.151:443: read: connection reset by peer"
t=2025-07-09T20:41:46-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T20:41:46-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T20:41:50-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=746388bee5f3 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T20:52:27-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=4d204d0892b8 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T21:09:25-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3c3a4a0a3372 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T21:09:25-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T21:09:26-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T21:09:26-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T21:26:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:50748->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-09T21:26:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T21:26:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T21:45:32-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fd325e2e0bc2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T21:50:20-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0586891e48e1 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T21:50:20-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T21:50:20-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T21:50:20-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T22:05:54-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:51099->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-09T22:05:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T22:05:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T22:38:14-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:51240->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-09T22:38:14-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T22:38:14-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T22:38:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3892332400af clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T22:38:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ceb1a09953d6 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T22:55:43-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:51371->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-09T22:55:43-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T22:55:43-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T22:56:31-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=99fc17f56640 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T22:56:41-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=271dea3f4d9b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T22:56:41-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-09T22:56:41-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T22:56:41-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T23:11:51-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:51837->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-09T23:11:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T23:11:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-09T23:12:06-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=911411d57ff2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-09T23:29:12-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:51954->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-09T23:29:12-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-09T23:29:12-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T00:02:00-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52159->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-10T00:02:00-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T00:02:00-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T00:18:31-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=69fac075568d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T00:26:51-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52293->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T00:26:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T00:26:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T00:26:56-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d230170d4f10 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T00:44:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52426->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-10T00:44:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T00:44:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T00:59:46-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1cb3fe48eb91 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T01:16:30-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52542->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T01:16:30-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T01:16:30-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T01:31:50-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1ff3843d43f7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T01:48:30-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52656->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-10T01:48:31-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T01:48:31-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T02:04:36-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=416821ee9d95 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T02:22:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52845->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-10T02:22:33-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T02:22:33-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T02:38:56-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=87544b3f3207 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T02:56:19-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:52985->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T02:56:19-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T02:56:19-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T02:56:27-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=51170b8d6f23 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T03:14:00-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ada1581dba47 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T03:31:44-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:53139->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-10T03:31:44-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T03:31:44-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T04:05:38-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:53345->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-10T04:05:38-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T04:05:38-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T04:21:40-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=996f1dd7548a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T04:38:52-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:53476->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T04:38:52-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T04:38:52-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T04:43:06-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=149457fd268f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T05:00:36-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9cfbc0b5fb10 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T05:32:24-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=220eef02c364 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T05:32:24-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T05:32:25-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T05:32:25-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T06:06:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:54004->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T06:06:09-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T06:06:09-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T06:37:48-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:54167->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T06:37:48-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T06:37:48-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T06:44:05-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d0cad2f5de33 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T06:44:11-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:54338->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-10T06:44:11-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T06:44:11-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T06:45:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=358b6abcc0cc clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T06:45:34-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c5a29570cba5 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T06:46:03-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=554b4fa45e35 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T06:46:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T06:46:03-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T06:46:03-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T07:03:56-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:54683->52.53.56.252:443: read: connection reset by peer"
t=2025-07-10T07:03:56-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T07:03:56-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T07:19:06-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:54775->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T07:19:06-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T07:19:06-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T07:19:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3ef78b11bed3 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T07:19:21-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=aef5c363e4a0 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T07:45:08-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:54901->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-10T07:45:08-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T07:45:08-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T07:45:23-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f4b82ab236df clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T08:01:53-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:55367->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-10T08:01:53-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T08:01:53-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T08:34:15-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:55589->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T08:34:16-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T08:34:16-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T08:34:20-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2458074ebf15 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T08:34:30-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8eb8f598e155 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T08:46:05-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=633488c13521 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T08:46:05-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T08:46:05-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T08:46:05-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T08:53:21-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:56053->52.53.75.151:443: read: connection reset by peer"
t=2025-07-10T08:53:21-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T08:53:21-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T09:00:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:56176->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T09:00:03-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T09:00:03-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T09:17:16-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f218ee15a186 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T09:18:06-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:56297->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-10T09:18:07-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T09:18:07-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T09:18:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a8191b57c730 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T09:19:21-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ecf220a16371 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T09:36:36-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:a1db:d9d3:d06d:d746]:56443->[2600:1f1c:d8:5f00::6e74:2]:443: read: no route to host"
t=2025-07-10T09:47:07-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=961231d8ec98 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T09:52:17-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-10T09:52:23-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T09:52:23-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T09:54:16-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b5379f7a18ef clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T09:54:16-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T09:54:17-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T09:54:17-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T10:46:47-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b4d7d19b75f2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T10:46:47-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T10:46:48-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T10:46:48-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T10:47:55-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-10T10:47:55-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T10:47:55-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T10:48:17-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7e756b6aa5f9 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T10:52:45-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=471ba4fb3ec8 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T10:52:45-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T10:52:46-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T10:52:46-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T11:01:30-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7a6d10beb7e9 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T11:01:30-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T11:01:31-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T11:01:31-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T11:24:09-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=631efc5b4553 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T11:24:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T11:24:09-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T11:24:09-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T12:07:20-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6d72f5caf28c clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T12:07:20-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T12:07:21-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T12:07:21-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T12:14:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:55945->***************:443: read: connection reset by peer"
t=2025-07-10T12:14:09-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T12:14:09-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T12:20:30-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:55999->*************:443: read: connection reset by peer"
t=2025-07-10T12:20:30-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T12:20:30-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T12:20:34-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=aab73866da94 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T12:20:44-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6940b1916ca0 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T12:57:38-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-10T12:57:40-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T12:57:40-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T12:57:54-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e050e391c416 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T13:31:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:62786->*************:443: read: connection reset by peer"
t=2025-07-10T13:31:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T13:31:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T13:31:24-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=52c56c111d75 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T14:26:51-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:51760->*************:443: read: connection reset by peer"
t=2025-07-10T14:26:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T14:26:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T14:27:06-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2a5419fb1667 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T15:33:11-0700 lvl=info msg="update available" obj=updater
t=2025-07-10T18:09:02-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=311a19b6ee3c clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T18:09:02-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T18:09:12-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-10T18:09:23-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-10T18:09:24-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T18:09:24-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T18:10:13-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-10T18:10:13-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T18:10:13-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T18:10:54-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b3718eb12319 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T18:28:23-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:51985->**************:443: read: can't assign requested address"
t=2025-07-10T18:28:27-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f18bbef66615 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T18:40:22-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-10T18:40:23-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T18:40:23-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T18:57:36-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:54451->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T18:57:36-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T18:57:36-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T19:28:43-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:54598->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T19:28:43-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T19:28:43-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T19:28:48-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e4c1f6bfe163 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T19:44:09-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=cc1898c499dc clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T20:01:01-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:54669->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-10T20:01:01-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T20:01:01-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T20:29:45-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:54802->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-10T20:29:45-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T20:29:45-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T20:46:35-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b350e95461bf clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T21:02:50-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:54865->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T21:02:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T21:02:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T21:02:55-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=be5bcbced985 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T21:20:07-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:54918->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T21:20:07-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T21:20:07-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T21:20:10-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7406bc789993 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T21:20:21-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fe1b4cf56915 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T21:46:49-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55023->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-10T21:46:49-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T21:46:49-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T22:07:13-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=18a83fbb6d65 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T22:25:10-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=51fc74a2d565 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T22:25:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-10T22:25:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T22:25:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T22:40:20-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55635->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T22:40:20-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T22:40:20-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T22:55:36-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=be5eceb9ee99 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T22:58:43-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55689->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T22:58:43-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T22:58:43-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T23:14:14-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ac30a68b98dd clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T23:14:19-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55773->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-10T23:14:19-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T23:14:19-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-10T23:14:34-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c40c6c67efc1 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-10T23:30:52-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55850->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-10T23:30:52-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-10T23:30:52-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T00:01:36-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55918->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-11T00:01:36-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T00:01:36-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T00:01:41-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8f178094391d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T00:35:28-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:55974->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-11T00:35:29-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T00:35:29-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T00:35:32-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a6a5b9b98680 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T01:05:50-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:56063->***************:443: read: connection reset by peer"
t=2025-07-11T01:05:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T01:05:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T01:05:54-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0de5bc21d486 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T01:21:21-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:56194->52.53.56.252:443: read: connection reset by peer"
t=2025-07-11T01:21:21-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T01:21:21-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T01:37:52-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=cae7f715dcb1 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T01:55:46-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56260->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-11T01:55:46-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T01:55:46-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T02:12:54-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2f55412f1c6b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T02:30:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56357->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-11T02:30:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T02:30:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T02:46:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fc281a8aea67 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T03:03:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56479->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-11T03:03:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T03:03:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T03:20:53-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1bc8c0efcb63 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T03:38:06-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56575->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-11T03:38:07-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T03:38:07-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T03:38:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=517643e866db clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T04:11:34-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56658->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-11T04:11:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T04:11:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T04:11:38-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=067dc5769c4b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T04:28:42-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56751->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-11T04:28:42-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T04:28:42-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T04:46:14-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c9513af21d12 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T05:02:28-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56844->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-11T05:02:28-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T05:02:28-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T05:18:16-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8d156f3d531b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T05:35:55-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:56991->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T05:35:55-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T05:35:55-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T05:51:57-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d946f3326b5a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T06:07:19-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57094->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T06:07:19-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T06:07:19-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T06:23:17-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=cf1d56e0177e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T06:39:17-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57226->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-11T06:39:18-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T06:39:18-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T06:54:22-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8d6aa3a65986 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T06:54:27-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57320->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-11T06:54:28-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T06:54:28-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T06:54:32-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1c3965958d9b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T06:54:42-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=16fecbab5134 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T07:10:17-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57401->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T07:10:17-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T07:10:17-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T07:18:39-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57448->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T07:18:48-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T07:18:48-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T07:18:52-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c79a6c33183d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T07:21:21-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8cfbfbf3ffe7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T07:41:07-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b3faaad3943a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T07:41:07-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T07:41:07-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T07:41:07-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T08:13:07-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57650->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-11T08:13:07-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T08:13:07-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T08:13:22-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=331dfeb78ab7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T08:31:28-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57787->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T08:31:28-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T08:31:28-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T09:00:04-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:57898->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T09:00:04-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T09:00:04-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T09:00:08-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=46a88c43cbec clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T09:17:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b9f6:f8c5:eb94:a14a]:58007->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-11T09:17:03-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T09:17:03-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T09:18:53-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6426ac37d853 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T09:21:47-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d0cbb1caabc3 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T09:23:47-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=64d063e7f882 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T09:23:47-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T09:24:38-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-11T09:24:40-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-11T09:24:41-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-11T09:24:43-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-11T09:24:47-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-11T09:24:55-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-11T09:42:35-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-11T09:54:41-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T09:54:41-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T09:58:28-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7bad00570e0c clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T09:58:28-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T09:58:28-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T09:58:28-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T10:27:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ab9813eb8454 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T10:27:19-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T10:27:20-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T10:27:20-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T11:09:49-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d584af741b34 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T11:09:49-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T11:09:49-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T11:09:49-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T11:10:57-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-11T11:10:58-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T11:10:58-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T11:11:03-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2929fe376a1f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T11:15:46-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=bba2bb57a211 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T11:15:46-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T11:15:46-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T11:15:46-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T11:16:51-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e76cbfa770a7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T11:16:51-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T11:16:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T11:16:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T12:19:05-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6b09b98cb38a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T12:19:05-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T12:19:06-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T12:19:06-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T12:22:25-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=bb9c47967666 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T12:22:25-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T12:22:25-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T12:22:25-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T12:27:23-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=49f1dcfd78ba clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T12:27:23-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T12:27:23-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T12:27:23-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T12:32:21-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=790a793176ed clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T12:32:21-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T12:32:21-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T12:32:21-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T12:37:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=be8a04165c44 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T12:37:19-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T12:37:19-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T12:37:19-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T12:42:26-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6984136e084e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T12:42:26-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T12:42:27-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T12:42:27-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T12:58:46-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:51910->************:443: read: connection reset by peer"
t=2025-07-11T12:58:46-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T12:58:46-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T12:59:01-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e6ab07667440 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T13:17:20-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:52001->*************:443: read: connection reset by peer"
t=2025-07-11T13:17:20-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T13:17:20-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T13:17:35-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8088586347e2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T13:38:05-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:52136->************:443: read: connection reset by peer"
t=2025-07-11T13:38:05-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T13:38:05-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T13:38:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=eca9a7fd2d82 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T13:52:53-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:52345->************:443: read: connection reset by peer"
t=2025-07-11T13:52:53-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T13:52:53-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T13:53:08-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9bca0ffb5216 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T14:30:12-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-11T14:30:12-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T14:30:12-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T14:30:26-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=65b0fd03a9da clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T14:33:56-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=535a94fe37b5 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T14:33:56-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T14:33:56-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T14:33:56-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T14:42:56-0700 lvl=info msg="update available" obj=updater
t=2025-07-11T15:42:02-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:53837->**************:443: read: connection reset by peer"
t=2025-07-11T15:42:02-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T15:42:02-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T15:42:17-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9901614037a7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T18:16:26-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=89166a416b20 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T18:16:26-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T18:16:36-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-11T18:16:39-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T18:16:39-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T18:35:46-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:60677->***************:443: read: can't assign requested address"
t=2025-07-11T18:53:43-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9e320ffa9e7e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T19:11:30-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-11T19:19:33-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T19:19:33-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T19:36:31-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:49592->***************:443: read: connection reset by peer"
t=2025-07-11T19:36:31-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T19:36:31-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T20:09:05-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:49793->*************:443: read: connection reset by peer"
t=2025-07-11T20:09:06-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T20:09:06-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T20:09:09-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=36a3c343280e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T20:28:54-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=650a4e798060 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T21:00:45-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=bd0a2f629c80 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T21:00:45-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T21:00:45-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T21:00:45-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T21:46:02-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f346ce136a22 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T21:46:02-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-11T21:46:02-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T21:46:02-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T22:16:44-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:50561->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-11T22:16:45-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T22:16:45-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T22:16:59-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0783ca6987fa clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T22:49:12-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:50714->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T22:49:12-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T22:49:12-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T23:21:54-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:50993->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-11T23:21:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T23:21:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T23:21:59-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=044d1dd87d08 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T23:22:09-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=235abed558d5 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-11T23:38:25-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:51123->************:443: read: connection reset by peer"
t=2025-07-11T23:38:26-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T23:38:26-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-11T23:44:41-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:51248->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-11T23:44:41-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-11T23:44:41-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T00:15:56-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:51372->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-12T00:15:56-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T00:15:56-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T00:15:59-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=4787901176ac clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T00:16:00-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=72b24eef4a35 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T00:49:05-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:51476->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-12T00:49:05-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T00:49:05-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T00:49:08-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=4dd0bf03f3ac clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T00:49:43-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=db1739e1338a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T00:49:44-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-12T00:49:44-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T00:49:44-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T00:50:01-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=234ca41a8b1b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T00:51:58-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=25c473401376 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T00:51:58-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T00:51:59-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T00:51:59-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T01:13:39-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e4fe3aaa4507 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T01:13:39-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T01:13:39-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T01:13:39-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T01:31:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:52188->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-12T01:31:03-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T01:31:03-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T01:31:18-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8cb8a7745fd2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T01:46:57-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:52293->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-12T01:46:57-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T01:46:57-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T02:20:53-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:52536->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-12T02:20:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T02:20:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T02:36:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=208c69f126b5 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T02:51:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:52662->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-12T02:51:33-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T02:51:33-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T03:08:32-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7a54d67acc89 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T03:24:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:52786->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-12T03:24:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T03:24:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T03:41:43-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=08552aa3a11b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T03:57:02-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:52881->52.53.75.151:443: read: connection reset by peer"
t=2025-07-12T03:57:02-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T03:57:02-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T03:57:06-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6591bfd7ddbb clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T04:29:51-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:53037->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-12T04:29:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T04:29:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T04:29:55-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d6e5963e9f6d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T05:01:47-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:53187->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-12T05:01:48-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T05:01:48-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T05:01:51-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8e2de6a51d1f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T05:19:30-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=271987296b7d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T05:34:56-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:53358->52.53.56.252:443: read: connection reset by peer"
t=2025-07-12T05:34:56-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T05:34:56-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T06:07:59-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:53599->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-12T06:07:59-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T06:07:59-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T06:24:24-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1052aae741fb clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T06:41:04-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:53746->52.53.75.151:443: read: connection reset by peer"
t=2025-07-12T06:41:04-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T06:41:04-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T06:58:58-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=5fa9c515ce5d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T07:02:47-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:53904->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-12T07:02:47-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T07:02:47-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T07:02:51-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b7d927043653 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T07:03:01-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=056f0b4adc55 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T07:20:53-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:54006->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-12T07:20:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T07:20:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T07:55:11-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:54165->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-12T07:55:11-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T07:55:11-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T08:03:49-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=da1504a41d6a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T08:03:54-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:54284->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-12T08:03:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T08:03:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T08:19:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=4ed958d9078f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T08:34:55-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:54379->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-12T08:34:56-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T08:34:56-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T08:50:34-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e9c3765c1a0a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T09:04:52-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:54503->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-12T09:04:52-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T09:04:52-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T09:10:10-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6d9e59258c64 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T09:25:32-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c79166b172c7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T09:40:42-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6595c12f1440 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T09:40:42-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T09:40:42-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T09:40:42-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T09:56:02-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:54884->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-12T09:56:02-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T09:56:02-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T09:56:17-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b9c2ffd29c77 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T10:00:42-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0f1f97b57e01 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T10:00:42-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T10:00:42-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T10:00:42-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T10:05:13-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=284a6feefced clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T10:05:13-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T10:05:13-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T10:05:13-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T10:09:38-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ced22dbd1eae clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T10:09:38-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T10:09:38-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T10:09:38-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T10:25:34-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:55756->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-12T10:25:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T10:25:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T10:31:30-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:55955->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-12T10:31:30-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T10:31:30-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T10:31:37-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f033a1de5caf clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T10:46:40-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:56031->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-12T10:46:40-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T10:46:40-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T10:46:44-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8088041cf857 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T10:46:55-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=67b494d559bb clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T10:49:07-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d304cc54f5b5 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T10:49:07-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T10:49:07-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T10:49:07-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T10:59:10-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1f324428082f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T10:59:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T10:59:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T10:59:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T11:06:00-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:56677->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-12T11:06:00-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T11:06:00-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T11:08:13-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b6ede9b8d1e2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T11:08:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=5b0c812df3ee clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T11:08:29-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T11:08:29-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T11:08:29-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T11:10:09-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=173faab7aaa3 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T11:10:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T11:10:09-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T11:10:09-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T11:29:12-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7f0b47a053b1 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T11:29:12-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T11:29:13-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T11:29:13-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T11:45:57-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:57484->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-12T11:45:57-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T11:45:57-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T12:07:01-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:57608->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-12T12:07:01-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T12:07:01-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T12:07:06-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=02f1a07ae22c clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T12:24:14-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:57741->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-12T12:24:15-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T12:24:15-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T12:29:05-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e000c33efab8 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T12:45:35-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=91508405917d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T13:04:07-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8fd22391b7f7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T13:04:07-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T13:04:07-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T13:04:07-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T13:24:27-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=00694881979f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T13:24:27-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T13:24:27-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T13:24:27-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T13:56:47-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:58381->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-12T13:56:47-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T13:56:47-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T14:00:05-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=057793b58a1b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T14:01:25-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f9f918766525 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T14:01:25-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T14:01:26-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T14:01:26-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T14:15:33-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=596c897abe7b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T14:15:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T14:15:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T14:15:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T14:17:16-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1185de308879 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T14:17:16-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T14:17:16-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T14:17:16-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T14:56:01-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=09865b014e2f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T14:56:01-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-12T14:56:01-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T14:56:01-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T15:10:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:59345->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-12T15:10:03-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T15:10:03-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T15:44:31-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:59465->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-12T15:44:31-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T15:44:31-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T15:50:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f9b5f3e6bfc1 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T15:50:16-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:59594->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-12T15:50:16-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T15:50:16-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T16:07:48-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=be77ce8f072e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T16:11:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:59712->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-12T16:11:03-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T16:11:03-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T16:11:08-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8562aebdd12a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T16:16:42-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:59817->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-12T16:16:42-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T16:16:42-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T16:16:47-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=40b6a65927a6 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T16:32:35-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:59906->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-12T16:32:35-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T16:32:35-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T16:32:39-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6815686a1fe5 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T16:48:46-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:59978->**************:443: read: connection reset by peer"
t=2025-07-12T16:48:46-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T16:48:46-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T16:48:49-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=611d37b3a579 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T16:49:00-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=03b355fedb71 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T17:04:06-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:60115->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-12T17:04:06-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T17:04:06-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T17:04:21-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3f50c5974c2b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T17:12:00-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:60285->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-12T17:12:00-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T17:12:00-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T17:23:12-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3352b2ae3346 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T17:23:18-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:60409->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-12T17:23:18-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T17:23:18-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T17:39:38-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9dbd09a402e4 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T17:54:42-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:60555->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-12T17:54:42-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T17:54:42-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T18:12:14-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:60698->**************:443: read: connection reset by peer"
t=2025-07-12T18:12:14-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T18:12:14-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T18:12:17-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f9fe624089db clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T18:29:14-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:60842->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-12T18:29:14-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T18:29:14-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T18:45:51-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=5ce0c41c789c clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T19:02:27-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:60949->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-12T19:02:27-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T19:02:27-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T19:13:16-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9ead033be62a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T19:13:22-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:61066->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-12T19:13:22-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T19:13:22-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T19:28:37-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3409e6b10626 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T19:44:15-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:61162->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-12T19:44:15-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T19:44:15-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T20:00:24-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e86075b92cc4 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T20:14:18-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:61309->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-12T20:14:18-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T20:14:18-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T20:29:39-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a5e1a0665523 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T20:29:47-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:61418->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-12T20:29:47-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T20:29:47-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T20:29:52-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=39771bb9664a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T20:45:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=77de214ded86 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T21:00:34-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:61529->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-12T21:00:35-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T21:00:35-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T21:30:55-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:61720->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-12T21:30:55-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T21:30:55-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T21:31:00-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=962b12354c88 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T21:31:10-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e26745239d36 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T22:04:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:61842->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-12T22:04:33-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T22:04:33-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T22:37:36-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:62085->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-12T22:37:37-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T22:37:37-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T22:54:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e5360ac33ae6 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T22:54:38-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:62220->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-12T22:54:38-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T22:54:38-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T22:54:43-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=827a7881dae4 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T23:11:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:62316->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-12T23:11:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-12T23:11:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-12T23:15:23-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=01beed9ddf30 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-12T23:32:48-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3fb981962b39 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T00:04:04-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6e8783f12bae clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T00:04:04-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-13T00:04:04-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T00:04:04-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T00:19:24-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:62715->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-13T00:19:24-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T00:19:24-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T00:19:39-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=76d2afbd4dd5 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T00:52:32-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:62847->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-13T00:52:32-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T00:52:32-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T01:27:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:63108->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-13T01:27:09-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T01:27:09-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T01:36:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=91c953ffc54f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T01:36:17-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:63242->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-13T01:36:17-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T01:36:17-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T01:52:02-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=5a1a65c92da8 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T02:07:16-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:63334->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-13T02:07:16-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T02:07:16-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T02:22:37-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6a68f0d35149 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T02:38:40-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:63475->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-13T02:38:40-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T02:38:40-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T02:56:39-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=420239e3e3bc clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T02:56:50-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:63586->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-13T02:56:50-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T02:56:50-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T02:56:55-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=5abffb38cc8a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T03:12:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:63697->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-13T03:12:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T03:12:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T03:29:13-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8d0e60500a0b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T03:44:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:63794->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-13T03:44:33-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T03:44:33-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T04:00:37-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1a2eed1f8128 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T04:18:30-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:63910->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-13T04:18:31-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T04:18:31-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T04:35:13-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2587939dfc4a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T04:50:34-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:64038->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-13T04:50:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T04:50:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T05:07:21-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fb6c02dae5ab clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T05:25:15-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:64187->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-13T05:25:15-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T05:25:15-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T05:42:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0be35285fe6a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T06:00:21-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:64307->52.53.56.252:443: read: connection reset by peer"
t=2025-07-13T06:00:21-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T06:00:21-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T06:01:35-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c5670abea029 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T06:17:57-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=626206fd7d9e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T06:49:37-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=35be952e1d31 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T06:49:37-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-13T06:49:37-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T06:49:37-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T07:35:52-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=11456653c0a1 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T07:35:52-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-13T07:35:52-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T07:35:52-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T07:51:51-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:64978->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-13T07:51:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T07:51:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T08:03:38-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:65125->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-13T08:03:38-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T08:03:38-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T08:03:42-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=63d163d331c1 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T08:03:59-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7f0fad64b245 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T08:19:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:65205->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-13T08:19:33-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T08:19:33-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T08:52:44-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:65380->*************:443: read: connection reset by peer"
t=2025-07-13T08:52:44-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T08:52:44-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T08:56:50-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=962d91dc85ec clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T08:56:51-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=cb73a338ed9b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T09:04:39-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=540724fdc340 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T09:04:39-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-13T09:04:40-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T09:04:40-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T09:20:20-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:49400->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-13T09:20:20-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T09:20:20-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T09:38:32-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=17c5a4b0beaa clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T09:49:13-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:49490->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-13T09:49:13-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T09:49:13-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T10:04:23-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:49641->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-13T10:04:23-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T10:04:23-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T10:04:27-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b4a0f9d1fab6 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T10:04:37-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d85fb12780ec clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T10:19:43-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:49755->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-13T10:19:43-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T10:19:43-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T10:19:57-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=229a19c39438 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T10:52:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:49881->************:443: read: connection reset by peer"
t=2025-07-13T10:52:09-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T10:52:09-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T11:05:45-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:50146->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-13T11:05:46-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T11:05:46-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T11:05:50-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=5bde7c449521 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T11:06:00-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2a14a44a2263 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T11:12:22-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:50253->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-13T11:12:22-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T11:12:22-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T11:20:30-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=cc289a8a2b17 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T11:23:20-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=23c85bab1fb5 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T11:23:20-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-13T11:23:20-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T11:23:20-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T11:39:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:50557->**************:443: read: connection reset by peer"
t=2025-07-13T11:39:04-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T11:39:04-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T12:06:40-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:50643->************:443: read: connection reset by peer"
t=2025-07-13T12:06:40-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T12:06:40-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T12:06:45-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7b7f6ff7e38b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T12:24:20-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:50770->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-13T12:24:20-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T12:24:20-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T12:39:56-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=230e9eec333f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T12:56:32-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:50883->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-13T12:56:32-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T12:56:32-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T13:07:42-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d8289bacf869 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T13:07:47-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:51025->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-13T13:07:47-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T13:07:47-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T13:07:52-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=19a7e20ca2cd clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T13:08:02-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b2a426302afc clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T13:24:17-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:51107->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-13T13:24:17-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T13:24:17-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T13:55:58-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:51258->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-13T13:55:58-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T13:55:58-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T14:01:06-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=cf0a9cdb34aa clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T14:08:44-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e101cf6090c9 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T14:26:44-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ebc448d4353a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T14:26:44-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-13T14:44:15-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:51606->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-13T14:44:16-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T14:44:16-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T15:09:43-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:51640->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-13T15:09:43-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T15:09:43-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T15:27:51-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=48f8dda70d76 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T15:44:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:51743->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-13T15:44:03-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T15:44:03-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T16:10:44-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:51972->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-13T16:10:45-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T16:10:45-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T16:10:49-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=524bf56a8f8b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T16:28:31-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:52084->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-13T16:28:31-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T16:28:31-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T16:44:09-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=5ac5ce78cde0 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T16:54:37-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:52186->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-13T16:54:37-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T16:54:37-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T17:10:46-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6a6c5198f550 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T17:25:52-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:52314->52.53.75.151:443: read: connection reset by peer"
t=2025-07-13T17:25:52-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T17:25:52-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T17:25:56-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c96ded8e46ec clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T17:26:06-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=286b64378669 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T17:58:50-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:52424->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-13T17:58:50-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T17:58:50-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T17:59:05-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3654a43c05b4 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T18:11:55-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=134a400edbb7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T18:11:55-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-13T18:11:55-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T18:11:55-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T18:43:38-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:52934->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-13T18:43:38-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T18:43:38-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T19:12:51-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:53061->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-13T19:12:52-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T19:12:52-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T19:12:56-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6be9af9453c8 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T19:28:00-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:53216->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-13T19:28:00-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T19:28:00-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T19:45:03-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=bee57d3f262d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T20:02:20-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:53295->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-13T20:02:20-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T20:02:20-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T20:13:52-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8051cebed754 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T20:31:26-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:53438->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-13T20:31:26-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T20:31:26-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T20:46:31-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fe90f7d05973 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T20:46:36-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:53533->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-13T20:46:36-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T20:46:36-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T20:46:41-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3a6382de29ef clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T20:46:51-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=bd2371aa5170 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T20:54:25-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:53651->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-13T20:54:25-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T20:54:25-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T21:09:59-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:53856->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-13T21:10:00-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T21:10:00-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T21:10:04-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c5d8c8f323c0 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T21:10:18-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=207f9a7718dc clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T21:11:41-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f7486376b001 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T21:11:41-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-13T21:11:41-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T21:11:41-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T21:31:39-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7a1d70a199d9 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T21:31:39-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-13T21:46:41-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: session closed"
t=2025-07-13T21:46:42-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T21:46:42-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T22:23:23-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=97c1ee394214 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T22:23:23-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-13T22:23:23-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T22:23:23-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T22:38:44-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:54753->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-13T22:38:44-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T22:38:44-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T22:54:08-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b55ade8a2609 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-13T23:11:49-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:54888->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-13T23:11:49-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T23:11:49-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T23:44:11-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:55142->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-13T23:44:11-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-13T23:44:11-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-13T23:59:37-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=aa3563be86d8 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T00:14:42-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:55282->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-14T00:14:42-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T00:14:42-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T00:14:46-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=777d374bf307 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T00:14:56-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d3722366e474 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T00:49:24-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:55407->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-14T00:49:24-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T00:49:24-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T01:04:34-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:55725->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-14T01:04:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T01:04:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T01:04:39-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=5988d829a8d6 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T01:04:49-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=08b62bb182cc clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T01:20:00-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:55828->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-14T01:20:01-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T01:20:01-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T01:37:00-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:55968->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-14T01:37:00-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T01:37:00-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T01:37:05-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3e46f8a4324e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T01:54:34-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:56076->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-14T02:12:08-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=92fd5e8bebe9 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T02:15:17-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: session closed"
t=2025-07-14T02:15:17-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T02:15:17-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T02:32:30-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1817b9c06c8c clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T02:48:24-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:56288->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-14T02:48:24-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T02:48:24-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T03:03:34-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:56398->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-14T03:03:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T03:03:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T03:03:39-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e04b3304883b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T03:03:49-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=16cf5a96c4a5 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T03:36:44-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:56528->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-14T03:36:44-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T03:36:44-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T04:11:38-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:56747->*************:443: read: connection reset by peer"
t=2025-07-14T04:11:38-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T04:11:38-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T04:11:42-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=55bd4125991a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T04:44:06-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:56878->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-14T04:44:06-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T04:44:06-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T04:44:09-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1cb1b90b6e73 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T05:04:54-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:57017->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-14T05:04:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T05:04:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T05:04:57-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=62a7921b65fe clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T05:05:15-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1e67bb2517ce clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T05:21:07-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:57136->**************:443: read: connection reset by peer"
t=2025-07-14T05:21:08-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T05:21:08-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T05:40:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:57341->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-14T05:40:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T05:40:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T05:40:14-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=584f75082bb1 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T05:40:44-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8bab1447964a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T05:57:35-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:57497->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-14T05:57:36-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T05:57:36-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T06:31:50-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:57648->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-14T06:31:50-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T06:31:50-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T06:47:35-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d6e6965e316d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T06:47:40-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:57783->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-14T06:47:40-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T06:47:40-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T07:03:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6c57e2b3d3a0 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T07:05:59-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:57895->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-14T07:05:59-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T07:05:59-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T07:21:03-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3cf6d981748d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T07:21:08-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:58011->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-14T07:21:09-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T07:21:09-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T07:21:13-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=772f29134f87 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T07:21:23-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8412cf7c9241 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T07:36:47-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:58106->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-14T07:36:47-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T07:36:47-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T08:06:58-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:58256->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-14T08:06:58-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T08:06:58-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T08:23:46-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=4b56de4a34de clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T08:39:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:58374->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-14T08:39:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T08:39:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T08:39:14-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c51728689e47 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T08:39:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0b795ec659e8 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T08:46:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:f898:3bb1:a32:ffa7]:58484->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-14T08:46:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T08:46:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T08:48:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9c9786268e06 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T08:51:31-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9faf8878de1f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T08:51:31-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-14T08:51:32-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T08:51:32-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T08:56:43-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2187ef559024 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T08:56:43-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-14T08:57:35-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-14T09:07:59-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-14T09:37:59-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-14T09:38:06-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-14T09:44:14-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-14T09:44:23-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T09:44:23-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T10:02:08-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-14T10:02:08-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T10:02:08-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T10:02:13-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=699d7841b97d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T10:31:37-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=bb1ed37cfb70 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T10:31:37-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-14T10:31:37-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T10:31:37-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T10:40:43-0700 lvl=info msg="join connections" obj=join id=38cd7dd0d38f l=[::1]:9084 r=3.86.255.204:27878
t=2025-07-14T10:40:43-0700 lvl=info msg="join connections" obj=join id=adc8a557e34b l=[::1]:9084 r=3.87.185.98:31494
t=2025-07-14T10:40:59-0700 lvl=info msg="join connections" obj=join id=2895e72457dd l=[::1]:9084 r=44.211.213.83:38090
t=2025-07-14T10:40:59-0700 lvl=info msg="join connections" obj=join id=e5d5b68ce4de l=[::1]:9084 r=54.147.185.201:20030
t=2025-07-14T10:42:43-0700 lvl=info msg="join connections" obj=join id=17278ba8a26c l=[::1]:9084 r=***********:39510
t=2025-07-14T10:42:43-0700 lvl=info msg="join connections" obj=join id=1420bafeeb87 l=[::1]:9084 r=************:39998
t=2025-07-14T10:43:02-0700 lvl=info msg="join connections" obj=join id=de03cf3bd580 l=[::1]:9084 r=***********:53428
t=2025-07-14T10:43:02-0700 lvl=info msg="join connections" obj=join id=3e08435ea243 l=[::1]:9084 r=**************:37308
t=2025-07-14T10:43:30-0700 lvl=warn msg="failed to open private leg" id=cc7252a538aa privaddr=localhost:9084 err="dial tcp [::1]:9084: connect: connection refused"
t=2025-07-14T10:43:35-0700 lvl=warn msg="failed to open private leg" id=abb62c7c0fdc privaddr=localhost:9084 err="dial tcp [::1]:9084: connect: connection refused"
t=2025-07-14T11:07:35-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=18f13b712aef clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T11:07:35-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-14T11:07:36-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T11:07:36-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T11:09:43-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=acb0510a0ba8 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T11:09:43-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-14T11:09:43-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T11:09:43-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T11:40:58-0700 lvl=info msg="update available" obj=updater
t=2025-07-14T11:48:59-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b65ac9629391 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T11:48:59-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-14T11:48:59-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T11:48:59-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T12:18:56-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:61555->**************:443: read: connection reset by peer"
t=2025-07-14T12:18:56-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T12:18:56-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T12:19:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d918faeeb51e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T12:39:53-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:52698->**************:443: read: connection reset by peer"
t=2025-07-14T12:39:53-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T12:39:53-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T12:49:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:56086->52.53.56.252:443: read: connection reset by peer"
t=2025-07-14T12:49:03-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T12:49:03-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T12:49:07-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=907f1bcc24d0 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T12:52:40-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3bcc9b786c8b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T13:08:21-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:56241->52.53.75.151:443: read: connection reset by peer"
t=2025-07-14T13:08:21-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T13:08:21-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T13:21:27-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:56420->52.53.75.151:443: read: connection reset by peer"
t=2025-07-14T13:21:27-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T13:21:27-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T13:21:31-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6ded32c3d491 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T13:33:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:56561->**************:443: read: connection reset by peer"
t=2025-07-14T13:33:09-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T13:33:09-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T13:33:14-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=4908d460af5f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T13:33:24-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=99780d7ed928 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T14:19:39-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-14T14:19:39-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T14:19:39-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T14:19:53-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7fc38b7e821b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T14:22:15-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-14T14:22:15-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T14:22:15-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T14:22:30-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=513d44d513ce clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T14:55:44-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-14T14:55:45-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T14:55:45-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T14:55:58-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=49e6adf0b4d7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T14:59:32-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=89b47ec22be5 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T14:59:32-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-14T14:59:33-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T14:59:33-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T15:28:02-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-14T15:28:02-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T15:28:02-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T15:28:14-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0bdc3e51a0f3 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T15:31:57-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1216baae07a5 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T15:31:57-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-14T15:31:57-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T15:31:57-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T17:24:10-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ae6286f6b00a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T17:24:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-14T17:24:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T17:24:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T17:42:46-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:50229->************:443: read: can't assign requested address"
t=2025-07-14T17:56:36-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2bc04d90cc05 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T17:56:39-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-14T17:56:40-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-14T17:56:41-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-14T17:56:43-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-14T17:58:57-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-14T18:00:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T18:00:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T18:07:26-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:53983->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-14T18:07:26-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T18:07:26-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T18:40:01-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:54096->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-14T18:40:01-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T18:40:01-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T18:56:36-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c8de33fbc823 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T19:11:55-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:54147->**************:443: read: connection reset by peer"
t=2025-07-14T19:11:55-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T19:11:55-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T19:11:59-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=66c9ebdf1a40 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T19:22:26-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:54225->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-14T19:22:26-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T19:22:26-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T19:39:23-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=332a3c0b20c5 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T19:57:00-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:54319->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-14T19:57:00-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T19:57:00-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T20:14:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a4c1f443999b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T20:23:23-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:54422->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-14T20:23:23-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T20:23:23-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T20:23:28-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e0c0cb03299c clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T20:38:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:54534->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-14T20:38:33-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T20:38:33-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T20:38:38-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d64a4e8f547b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T20:38:48-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1f25fdacddd7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T20:39:51-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-14T20:39:51-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T20:39:51-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T20:55:26-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:54692->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-14T20:55:26-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T20:55:26-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T20:55:32-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e561cf5ffe37 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T21:10:36-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:54730->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-14T21:10:36-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T21:10:36-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T21:10:40-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=231378f5f9e3 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T21:24:28-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:54785->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-14T21:24:28-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T21:24:28-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T21:41:40-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1e5acabb3447 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T21:56:45-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:54827->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-14T21:56:45-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T21:56:45-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T21:56:50-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ca34fb1f4a05 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T21:57:00-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fade52b74ae9 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T22:28:27-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:54915->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-14T22:28:27-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T22:28:27-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T22:28:42-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=92958c629c22 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T22:35:21-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:55037->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-14T22:35:21-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T22:35:21-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T22:52:05-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:55133->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-14T22:52:05-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T22:52:05-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T22:52:09-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0209ceb29232 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T23:24:05-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:55159->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-14T23:24:05-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T23:24:05-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T23:55:54-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:55242->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-14T23:55:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-14T23:55:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-14T23:55:56-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7594e4f717bf clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-14T23:55:58-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3f0ade7c45d2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T00:29:58-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:55332->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-15T00:29:58-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T00:29:58-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T00:30:01-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=73794f8bcc51 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T01:03:11-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:55442->**************:443: read: connection reset by peer"
t=2025-07-15T01:03:11-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T01:03:11-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T01:03:14-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6fcd303ac51e clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T01:38:02-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:55547->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-15T01:38:02-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T01:38:02-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T01:38:05-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=02c245150b65 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T02:10:30-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:55645->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-15T02:10:30-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T02:10:30-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T02:10:33-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e6797ed21fee clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T02:43:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:55751->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-15T02:43:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T02:43:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T02:43:13-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=498cb7ae4d1c clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T03:16:31-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:55840->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-15T03:16:31-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T03:16:31-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T03:16:34-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=361c4bf8ad99 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T03:31:41-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:55938->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-15T03:31:41-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T03:31:41-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T03:31:45-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d2c8d230d67f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T03:31:56-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=575863d6cab4 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T03:49:22-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:56020->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-15T03:49:22-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T03:49:22-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T04:06:14-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:56089->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-15T04:06:15-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T04:06:15-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T04:21:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=5f7fa0fdfd68 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T04:21:24-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:56143->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-15T04:21:25-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T04:21:25-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T04:21:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=833100767ebd clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T04:36:43-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c4d8aa21825b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T04:53:00-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:56199->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-15T04:53:01-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T04:53:01-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T05:13:46-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:56295->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-15T05:13:46-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T05:13:46-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T05:13:51-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=95696691a05d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T05:29:16-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:56372->************:443: read: connection reset by peer"
t=2025-07-15T05:29:16-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T05:29:16-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T05:44:21-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6a4f6c1d5fcb clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T05:44:26-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:56453->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-15T05:44:26-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T05:44:26-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T05:44:31-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=cfcd82f3ec38 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T06:00:44-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=45611d3b61a0 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T06:17:25-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:56539->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-15T06:17:25-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T06:17:25-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T06:48:20-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:56640->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-15T06:48:20-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T06:48:20-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T06:48:24-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7bdb83cfa347 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T06:48:34-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=64f198241824 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T07:05:56-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:56757->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-15T07:05:56-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T07:05:56-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T07:14:50-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:56883->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-15T07:14:50-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T07:14:50-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T07:15:24-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=bb7a923b179d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T07:15:28-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-15T07:15:28-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T07:15:28-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T07:49:01-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8d2e40ca8d7a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T07:49:05-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:56988->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-15T07:49:05-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T07:49:05-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T08:04:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=36ae1db5b882 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T08:04:15-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:57094->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-15T08:04:15-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T08:04:15-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T08:04:20-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=bf860ecbddf9 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T08:04:30-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8b34bbeb212f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T08:15:47-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:57146->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-15T08:15:47-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T08:15:47-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T08:31:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:57225->52.53.56.252:443: read: connection reset by peer"
t=2025-07-15T08:31:03-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T08:31:03-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T08:46:08-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=af3e45cd35d1 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T08:46:13-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:57261->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-15T08:46:13-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T08:46:13-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T08:46:18-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2f7af2c9ee3a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T08:46:28-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=43985ac67c7a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T09:00:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:57363->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-15T09:00:03-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T09:00:03-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T09:16:08-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:b868:5c5e:cbd:7e4f]:57407->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-15T09:16:08-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T09:16:08-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T09:21:12-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c2bc36c61a59 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T09:23:00-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=25d715e98bd9 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T09:23:52-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8fca89a7c3a0 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T09:23:52-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-15T09:41:32-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T09:51:34-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T09:52:28-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T09:53:28-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T09:53:35-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T09:53:35-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T09:55:47-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-15T09:55:48-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T09:55:48-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T09:56:02-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=edbbc4c78f2a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T10:40:11-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=40d2e841ad94 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T10:40:11-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-15T10:40:12-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T10:40:12-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T11:22:49-0700 lvl=info msg="update available" obj=updater
t=2025-07-15T12:32:05-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6bf75784d18f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T12:32:05-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-15T12:32:05-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T12:32:05-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T12:42:24-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:53850->**************:443: read: connection reset by peer"
t=2025-07-15T12:42:24-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T12:42:24-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T12:42:39-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1b5c34aa4a3b clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T13:25:54-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-15T13:25:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T13:25:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T13:26:07-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b05797c66216 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T13:29:09-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3ae34421b3fa clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T13:29:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-15T13:29:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T13:29:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T13:38:47-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=cfaf9068cc58 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T13:38:47-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-15T13:38:47-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T13:38:47-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T16:17:54-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=773d66ff72d0 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T16:17:54-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-15T16:17:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T16:17:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T16:20:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-15T16:20:33-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T16:20:33-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T16:20:48-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9e1556ec0379 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T17:19:58-0700 lvl=info msg="join connections" obj=join id=0ba2b62562c9 l=[::1]:9084 r=44.220.134.162:52614
t=2025-07-15T17:19:58-0700 lvl=info msg="join connections" obj=join id=0d907ed8ec53 l=[::1]:9084 r=44.206.249.68:28578
t=2025-07-15T17:20:03-0700 lvl=info msg="join connections" obj=join id=d6a58ab28970 l=[::1]:9084 r=34.224.91.252:24644
t=2025-07-15T17:20:03-0700 lvl=info msg="join connections" obj=join id=a75a455c2be2 l=[::1]:9084 r=54.224.48.234:24268
t=2025-07-15T17:21:38-0700 lvl=info msg="join connections" obj=join id=d923f8e83800 l=[::1]:9084 r=54.174.190.243:48802
t=2025-07-15T17:21:38-0700 lvl=info msg="join connections" obj=join id=c6bbde7439b1 l=[::1]:9084 r=13.221.183.254:34432
t=2025-07-15T17:21:44-0700 lvl=info msg="join connections" obj=join id=237f40f07dfb l=[::1]:9084 r=54.167.22.169:21190
t=2025-07-15T17:21:44-0700 lvl=info msg="join connections" obj=join id=5ff566a43c1a l=[::1]:9084 r=3.86.46.50:43928
t=2025-07-15T17:25:54-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f3efdd12866d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T17:25:54-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-15T17:25:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T17:25:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T17:30:33-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=51984b74d797 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T17:30:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-15T17:30:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T17:30:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T17:32:32-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-15T17:32:32-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T17:32:32-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T17:33:01-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e17fef9a936a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T17:34:58-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=353f58e0f95f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T17:34:58-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-15T17:35:38-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:36:13-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:36:25-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:36:38-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:36:52-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:39:54-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:41:41-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:42:26-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:43:57-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:46:16-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:46:57-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:48:01-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:49:16-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:49:58-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:51:58-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:52:39-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:56:57-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T17:58:26-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T18:00:14-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T18:01:22-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T18:02:03-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T18:02:43-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T18:06:24-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T18:07:31-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-15T18:09:56-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T18:09:56-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T18:10:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-15T18:10:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T18:10:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T18:10:44-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=5b9f6d21fa98 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T19:04:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d6f8c073a196 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T19:04:29-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-15T19:04:30-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T19:04:30-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T19:19:54-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:58933->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-15T19:19:54-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T19:19:54-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T19:26:40-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f9579508de88 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T19:28:35-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:59014->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-15T19:28:35-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T19:28:35-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T19:43:45-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:59092->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-15T19:43:45-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T19:43:45-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T19:43:50-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=579bec336149 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T19:44:00-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0fda7a700c94 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T20:27:38-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:59167->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-15T20:27:38-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9de481d07b80 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T20:27:38-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T20:27:38-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T21:01:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:59337->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-15T21:01:33-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T21:01:33-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T21:28:39-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:59452->*************:443: read: connection reset by peer"
t=2025-07-15T21:28:39-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T21:28:39-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T21:28:43-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=730e32af17c7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T21:45:04-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:59535->*************:443: read: connection reset by peer"
t=2025-07-15T21:45:04-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T21:45:04-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T22:00:08-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9715ce5ae494 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T22:00:14-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:59612->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-15T22:00:14-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T22:00:14-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T22:00:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a7c08096ee2a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T22:00:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=86fa59b94886 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T22:16:25-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:59678->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-15T22:16:25-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T22:16:25-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T22:35:27-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:59746->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-15T22:35:27-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T22:35:27-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-15T22:37:27-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=be254e65b4e1 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T22:55:18-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c8d55d65342a clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T23:28:50-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0afb5b2c3e40 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-15T23:28:50-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-15T23:28:50-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-15T23:28:50-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T00:03:12-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:59988->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-16T00:03:12-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T00:03:12-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T00:21:14-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:60081->[2600:1f1c:d8:5f00::6e74:0]:443: read: connection reset by peer"
t=2025-07-16T00:21:14-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T00:21:14-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T00:36:36-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0dea26b58380 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T00:54:03-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:60129->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-16T00:54:04-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T00:54:04-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T01:11:10-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=307aece8c24f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T01:29:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:60249->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-16T01:29:09-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T01:29:09-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T01:37:31-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fbb1ec1da278 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T01:37:36-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:60361->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-16T01:37:37-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T01:37:37-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T01:37:43-0700 lvl=info msg="update available" obj=updater
t=2025-07-16T01:37:46-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d7e8f1c01a8f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T01:54:31-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c8dd532fde3d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T02:10:22-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:60420->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-16T02:10:22-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T02:10:22-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T02:42:55-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:60530->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-16T02:42:55-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T02:42:55-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T02:57:59-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c5ad12720688 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T02:58:05-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:60635->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-16T02:58:05-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T02:58:05-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T02:58:09-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7752629cee75 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T02:58:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=645ea6534ca2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T03:15:00-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:60720->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-16T03:15:00-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T03:15:00-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T03:30:10-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:60771->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-16T03:30:10-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T03:30:10-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T03:30:15-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=62e55de752ff clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T03:47:04-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c39a6058d3bf clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T04:02:15-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:60836->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-16T04:02:15-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T04:02:15-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T04:36:37-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:60920->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-16T04:36:37-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T04:36:37-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T04:53:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=90ca91303960 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T04:53:27-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:61002->***************:443: read: connection reset by peer"
t=2025-07-16T04:53:27-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T04:53:27-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T04:53:31-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ccfc84a54e27 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T05:10:58-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:61104->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-16T05:10:58-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T05:10:58-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T05:20:04-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2045f026730f clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T05:36:38-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:61162->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-16T05:36:38-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T05:36:38-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T05:36:42-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2e7fec9a585d clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T05:40:47-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=49402b20bda6 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T05:50:05-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9459e5c1cdf3 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T05:50:05-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-16T05:50:05-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T05:50:05-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T06:05:15-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:61456->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-16T06:05:15-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T06:05:15-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T06:21:54-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=547e9f39bb75 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T06:38:09-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:61524->[2600:1f1c:d8:5f00::6e74:4]:443: read: connection reset by peer"
t=2025-07-16T06:38:09-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T06:38:09-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T07:10:15-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:61596->**************:443: read: connection reset by peer"
t=2025-07-16T07:10:15-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T07:10:15-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T07:10:19-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d330c4377fbb clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T07:14:39-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=05928c25cbe2 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T07:21:05-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=14d7a1406c48 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T07:21:05-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-16T07:21:06-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T07:21:06-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T07:54:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:61806->[2600:1f1c:d8:5f00::6e74:2]:443: read: connection reset by peer"
t=2025-07-16T07:54:33-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T07:54:33-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T08:12:43-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:61873->[2600:1f1c:d8:5f01::6e74:3]:443: read: connection reset by peer"
t=2025-07-16T08:12:43-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T08:12:43-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T08:12:46-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=65744b203425 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T08:22:04-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:61953->[2600:1f1c:d8:5f01::6e74:1]:443: read: connection reset by peer"
t=2025-07-16T08:22:04-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T08:22:04-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T08:22:08-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a90d09b4a8be clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T08:37:33-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp [2601:643:8a80:1cb0:20b8:4bc4:9a16:d72a]:61981->[2600:1f1c:d8:5f01::6e74:5]:443: read: connection reset by peer"
t=2025-07-16T08:37:33-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T08:37:33-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T08:38:23-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ce73564cbbd7 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T08:38:59-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=bf3cd7236fde clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T08:41:53-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b6be583e2909 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T08:41:53-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-16T08:42:03-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-16T08:42:59-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-16T08:59:37-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: no such host"
t=2025-07-16T09:15:37-0700 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-16T09:15:44-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T09:15:44-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T09:22:14-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read tcp **********:49491->*************:443: read: connection reset by peer"
t=2025-07-16T09:22:14-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T09:22:14-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T09:22:29-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=aa18009f2bc9 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T10:12:34-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6aa6037f0f96 clientid=0b47db377ac53e4a10fcb402d717d21a
t=2025-07-16T10:12:34-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="session closed"
t=2025-07-16T10:12:34-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T10:12:34-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T10:13:13-0700 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5f728e7de670 err="read EOF from remote peer"
t=2025-07-16T10:13:13-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-16T10:13:13-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-16T10:13:32-0700 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3a13768651a6 clientid=0b47db377ac53e4a10fcb402d717d21a
