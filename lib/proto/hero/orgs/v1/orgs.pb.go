// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.3
// 	protoc        (unknown)
// source: hero/orgs/v1/orgs.proto

package orgs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	v2 "proto/hero/assets/v2"
	_ "proto/hero/permissions/v1"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServiceType int32

const (
	ServiceType_SERVICE_TYPE_UNSPECIFIED ServiceType = 0
	ServiceType_SERVICE_TYPE_DEMO        ServiceType = 1
	ServiceType_SERVICE_TYPE_PRODUCTION  ServiceType = 2
)

// Enum value maps for ServiceType.
var (
	ServiceType_name = map[int32]string{
		0: "SERVICE_TYPE_UNSPECIFIED",
		1: "SERVICE_TYPE_DEMO",
		2: "SERVICE_TYPE_PRODUCTION",
	}
	ServiceType_value = map[string]int32{
		"SERVICE_TYPE_UNSPECIFIED": 0,
		"SERVICE_TYPE_DEMO":        1,
		"SERVICE_TYPE_PRODUCTION":  2,
	}
)

func (x ServiceType) Enum() *ServiceType {
	p := new(ServiceType)
	*p = x
	return p
}

func (x ServiceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_orgs_v1_orgs_proto_enumTypes[0].Descriptor()
}

func (ServiceType) Type() protoreflect.EnumType {
	return &file_hero_orgs_v1_orgs_proto_enumTypes[0]
}

func (x ServiceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceType.Descriptor instead.
func (ServiceType) EnumDescriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{0}
}

type TurnOnGuestModeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TurnOnGuestModeRequest) Reset() {
	*x = TurnOnGuestModeRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TurnOnGuestModeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TurnOnGuestModeRequest) ProtoMessage() {}

func (x *TurnOnGuestModeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TurnOnGuestModeRequest.ProtoReflect.Descriptor instead.
func (*TurnOnGuestModeRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{0}
}

func (x *TurnOnGuestModeRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

type TurnOnGuestModeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TurnOnGuestModeResponse) Reset() {
	*x = TurnOnGuestModeResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TurnOnGuestModeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TurnOnGuestModeResponse) ProtoMessage() {}

func (x *TurnOnGuestModeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TurnOnGuestModeResponse.ProtoReflect.Descriptor instead.
func (*TurnOnGuestModeResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{1}
}

func (x *TurnOnGuestModeResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type TurnOffGuestModeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TurnOffGuestModeRequest) Reset() {
	*x = TurnOffGuestModeRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TurnOffGuestModeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TurnOffGuestModeRequest) ProtoMessage() {}

func (x *TurnOffGuestModeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TurnOffGuestModeRequest.ProtoReflect.Descriptor instead.
func (*TurnOffGuestModeRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{2}
}

func (x *TurnOffGuestModeRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

type TurnOffGuestModeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TurnOffGuestModeResponse) Reset() {
	*x = TurnOffGuestModeResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TurnOffGuestModeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TurnOffGuestModeResponse) ProtoMessage() {}

func (x *TurnOffGuestModeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TurnOffGuestModeResponse.ProtoReflect.Descriptor instead.
func (*TurnOffGuestModeResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{3}
}

func (x *TurnOffGuestModeResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type GetOrgAPIUserPrivateByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrgAPIUserPrivateByIdRequest) Reset() {
	*x = GetOrgAPIUserPrivateByIdRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrgAPIUserPrivateByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrgAPIUserPrivateByIdRequest) ProtoMessage() {}

func (x *GetOrgAPIUserPrivateByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrgAPIUserPrivateByIdRequest.ProtoReflect.Descriptor instead.
func (*GetOrgAPIUserPrivateByIdRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{4}
}

func (x *GetOrgAPIUserPrivateByIdRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetOrgAPIUserPrivateByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgApiUser    *OrgApiUserPrivate     `protobuf:"bytes,1,opt,name=org_api_user,json=orgApiUser,proto3" json:"org_api_user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrgAPIUserPrivateByIdResponse) Reset() {
	*x = GetOrgAPIUserPrivateByIdResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrgAPIUserPrivateByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrgAPIUserPrivateByIdResponse) ProtoMessage() {}

func (x *GetOrgAPIUserPrivateByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrgAPIUserPrivateByIdResponse.ProtoReflect.Descriptor instead.
func (*GetOrgAPIUserPrivateByIdResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{5}
}

func (x *GetOrgAPIUserPrivateByIdResponse) GetOrgApiUser() *OrgApiUserPrivate {
	if x != nil {
		return x.OrgApiUser
	}
	return nil
}

type CreateOrgRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Org           *Org                   `protobuf:"bytes,1,opt,name=org,proto3" json:"org,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrgRequest) Reset() {
	*x = CreateOrgRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrgRequest) ProtoMessage() {}

func (x *CreateOrgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrgRequest.ProtoReflect.Descriptor instead.
func (*CreateOrgRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{6}
}

func (x *CreateOrgRequest) GetOrg() *Org {
	if x != nil {
		return x.Org
	}
	return nil
}

type CreateOrgResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Org           *Org                   `protobuf:"bytes,1,opt,name=org,proto3" json:"org,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrgResponse) Reset() {
	*x = CreateOrgResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrgResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrgResponse) ProtoMessage() {}

func (x *CreateOrgResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrgResponse.ProtoReflect.Descriptor instead.
func (*CreateOrgResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{7}
}

func (x *CreateOrgResponse) GetOrg() *Org {
	if x != nil {
		return x.Org
	}
	return nil
}

type UpdateOrgRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Org           *Org                   `protobuf:"bytes,1,opt,name=org,proto3" json:"org,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOrgRequest) Reset() {
	*x = UpdateOrgRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOrgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrgRequest) ProtoMessage() {}

func (x *UpdateOrgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrgRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrgRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateOrgRequest) GetOrg() *Org {
	if x != nil {
		return x.Org
	}
	return nil
}

type UpdateOrgResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Org           *Org                   `protobuf:"bytes,1,opt,name=org,proto3" json:"org,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOrgResponse) Reset() {
	*x = UpdateOrgResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOrgResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrgResponse) ProtoMessage() {}

func (x *UpdateOrgResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrgResponse.ProtoReflect.Descriptor instead.
func (*UpdateOrgResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateOrgResponse) GetOrg() *Org {
	if x != nil {
		return x.Org
	}
	return nil
}

type DeleteOrgRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteOrgRequest) Reset() {
	*x = DeleteOrgRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteOrgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteOrgRequest) ProtoMessage() {}

func (x *DeleteOrgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteOrgRequest.ProtoReflect.Descriptor instead.
func (*DeleteOrgRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteOrgRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteOrgResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteOrgResponse) Reset() {
	*x = DeleteOrgResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteOrgResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteOrgResponse) ProtoMessage() {}

func (x *DeleteOrgResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteOrgResponse.ProtoReflect.Descriptor instead.
func (*DeleteOrgResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{11}
}

type GetOrgRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrgRequest) Reset() {
	*x = GetOrgRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrgRequest) ProtoMessage() {}

func (x *GetOrgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrgRequest.ProtoReflect.Descriptor instead.
func (*GetOrgRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{12}
}

func (x *GetOrgRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetOrgResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Org           *Org                   `protobuf:"bytes,1,opt,name=org,proto3" json:"org,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrgResponse) Reset() {
	*x = GetOrgResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrgResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrgResponse) ProtoMessage() {}

func (x *GetOrgResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrgResponse.ProtoReflect.Descriptor instead.
func (*GetOrgResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{13}
}

func (x *GetOrgResponse) GetOrg() *Org {
	if x != nil {
		return x.Org
	}
	return nil
}

type ListOrgsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrgsRequest) Reset() {
	*x = ListOrgsRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrgsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrgsRequest) ProtoMessage() {}

func (x *ListOrgsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrgsRequest.ProtoReflect.Descriptor instead.
func (*ListOrgsRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{14}
}

type ListOrgsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Orgs          []*Org                 `protobuf:"bytes,1,rep,name=orgs,proto3" json:"orgs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrgsResponse) Reset() {
	*x = ListOrgsResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrgsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrgsResponse) ProtoMessage() {}

func (x *ListOrgsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrgsResponse.ProtoReflect.Descriptor instead.
func (*ListOrgsResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{15}
}

func (x *ListOrgsResponse) GetOrgs() []*Org {
	if x != nil {
		return x.Orgs
	}
	return nil
}

type ValidateOrgCredsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateOrgCredsRequest) Reset() {
	*x = ValidateOrgCredsRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateOrgCredsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateOrgCredsRequest) ProtoMessage() {}

func (x *ValidateOrgCredsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateOrgCredsRequest.ProtoReflect.Descriptor instead.
func (*ValidateOrgCredsRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{16}
}

func (x *ValidateOrgCredsRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ValidateOrgCredsRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type ValidateOrgCredsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Valid         bool                   `protobuf:"varint,1,opt,name=valid,proto3" json:"valid,omitempty"`
	OrgApiUser    *OrgApiUser            `protobuf:"bytes,2,opt,name=org_api_user,json=orgApiUser,proto3" json:"org_api_user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateOrgCredsResponse) Reset() {
	*x = ValidateOrgCredsResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateOrgCredsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateOrgCredsResponse) ProtoMessage() {}

func (x *ValidateOrgCredsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateOrgCredsResponse.ProtoReflect.Descriptor instead.
func (*ValidateOrgCredsResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{17}
}

func (x *ValidateOrgCredsResponse) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

func (x *ValidateOrgCredsResponse) GetOrgApiUser() *OrgApiUser {
	if x != nil {
		return x.OrgApiUser
	}
	return nil
}

type OrgApiUser struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	OrgId             int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	Username          string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	EncryptedPassword string                 `protobuf:"bytes,4,opt,name=encrypted_password,json=encryptedPassword,proto3" json:"encrypted_password,omitempty"`
	HashedPassword    string                 `protobuf:"bytes,5,opt,name=hashed_password,json=hashedPassword,proto3" json:"hashed_password,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *OrgApiUser) Reset() {
	*x = OrgApiUser{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrgApiUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrgApiUser) ProtoMessage() {}

func (x *OrgApiUser) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrgApiUser.ProtoReflect.Descriptor instead.
func (*OrgApiUser) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{18}
}

func (x *OrgApiUser) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OrgApiUser) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *OrgApiUser) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *OrgApiUser) GetEncryptedPassword() string {
	if x != nil {
		return x.EncryptedPassword
	}
	return ""
}

func (x *OrgApiUser) GetHashedPassword() string {
	if x != nil {
		return x.HashedPassword
	}
	return ""
}

func (x *OrgApiUser) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *OrgApiUser) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type OrgApiUserPrivate struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	OrgId             int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	Username          string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	EncryptedPassword string                 `protobuf:"bytes,4,opt,name=encrypted_password,json=encryptedPassword,proto3" json:"encrypted_password,omitempty"`
	HashedPassword    string                 `protobuf:"bytes,5,opt,name=hashed_password,json=hashedPassword,proto3" json:"hashed_password,omitempty"`
	RawPassword       string                 `protobuf:"bytes,6,opt,name=raw_password,json=rawPassword,proto3" json:"raw_password,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *OrgApiUserPrivate) Reset() {
	*x = OrgApiUserPrivate{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrgApiUserPrivate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrgApiUserPrivate) ProtoMessage() {}

func (x *OrgApiUserPrivate) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrgApiUserPrivate.ProtoReflect.Descriptor instead.
func (*OrgApiUserPrivate) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{19}
}

func (x *OrgApiUserPrivate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OrgApiUserPrivate) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *OrgApiUserPrivate) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *OrgApiUserPrivate) GetEncryptedPassword() string {
	if x != nil {
		return x.EncryptedPassword
	}
	return ""
}

func (x *OrgApiUserPrivate) GetHashedPassword() string {
	if x != nil {
		return x.HashedPassword
	}
	return ""
}

func (x *OrgApiUserPrivate) GetRawPassword() string {
	if x != nil {
		return x.RawPassword
	}
	return ""
}

func (x *OrgApiUserPrivate) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *OrgApiUserPrivate) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type OrgQueue struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FriendlyName   string                 `protobuf:"bytes,2,opt,name=friendly_name,json=friendlyName,proto3" json:"friendly_name,omitempty"`
	TwilioQueueSid string                 `protobuf:"bytes,3,opt,name=twilio_queue_sid,json=twilioQueueSid,proto3" json:"twilio_queue_sid,omitempty"`
	Description    string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	OrgId          int32                  `protobuf:"varint,7,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *OrgQueue) Reset() {
	*x = OrgQueue{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrgQueue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrgQueue) ProtoMessage() {}

func (x *OrgQueue) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrgQueue.ProtoReflect.Descriptor instead.
func (*OrgQueue) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{20}
}

func (x *OrgQueue) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrgQueue) GetFriendlyName() string {
	if x != nil {
		return x.FriendlyName
	}
	return ""
}

func (x *OrgQueue) GetTwilioQueueSid() string {
	if x != nil {
		return x.TwilioQueueSid
	}
	return ""
}

func (x *OrgQueue) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *OrgQueue) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *OrgQueue) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *OrgQueue) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

type InsertOrgQueueRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FriendlyName   string                 `protobuf:"bytes,1,opt,name=friendly_name,json=friendlyName,proto3" json:"friendly_name,omitempty"`
	TwilioQueueSid string                 `protobuf:"bytes,2,opt,name=twilio_queue_sid,json=twilioQueueSid,proto3" json:"twilio_queue_sid,omitempty"`
	Description    string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	OrgId          int32                  `protobuf:"varint,4,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *InsertOrgQueueRequest) Reset() {
	*x = InsertOrgQueueRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InsertOrgQueueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsertOrgQueueRequest) ProtoMessage() {}

func (x *InsertOrgQueueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsertOrgQueueRequest.ProtoReflect.Descriptor instead.
func (*InsertOrgQueueRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{21}
}

func (x *InsertOrgQueueRequest) GetFriendlyName() string {
	if x != nil {
		return x.FriendlyName
	}
	return ""
}

func (x *InsertOrgQueueRequest) GetTwilioQueueSid() string {
	if x != nil {
		return x.TwilioQueueSid
	}
	return ""
}

func (x *InsertOrgQueueRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *InsertOrgQueueRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

type InsertOrgQueueResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgQueue      *OrgQueue              `protobuf:"bytes,1,opt,name=org_queue,json=orgQueue,proto3" json:"org_queue,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InsertOrgQueueResponse) Reset() {
	*x = InsertOrgQueueResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InsertOrgQueueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsertOrgQueueResponse) ProtoMessage() {}

func (x *InsertOrgQueueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsertOrgQueueResponse.ProtoReflect.Descriptor instead.
func (*InsertOrgQueueResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{22}
}

func (x *InsertOrgQueueResponse) GetOrgQueue() *OrgQueue {
	if x != nil {
		return x.OrgQueue
	}
	return nil
}

type CreateOrgTwilioQueueRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	FriendlyName  string                 `protobuf:"bytes,2,opt,name=friendly_name,json=friendlyName,proto3" json:"friendly_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrgTwilioQueueRequest) Reset() {
	*x = CreateOrgTwilioQueueRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrgTwilioQueueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrgTwilioQueueRequest) ProtoMessage() {}

func (x *CreateOrgTwilioQueueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrgTwilioQueueRequest.ProtoReflect.Descriptor instead.
func (*CreateOrgTwilioQueueRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{23}
}

func (x *CreateOrgTwilioQueueRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *CreateOrgTwilioQueueRequest) GetFriendlyName() string {
	if x != nil {
		return x.FriendlyName
	}
	return ""
}

type CreateOrgTwilioQueueResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	QueueSid      string                 `protobuf:"bytes,1,opt,name=queue_sid,json=queueSid,proto3" json:"queue_sid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrgTwilioQueueResponse) Reset() {
	*x = CreateOrgTwilioQueueResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrgTwilioQueueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrgTwilioQueueResponse) ProtoMessage() {}

func (x *CreateOrgTwilioQueueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrgTwilioQueueResponse.ProtoReflect.Descriptor instead.
func (*CreateOrgTwilioQueueResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{24}
}

func (x *CreateOrgTwilioQueueResponse) GetQueueSid() string {
	if x != nil {
		return x.QueueSid
	}
	return ""
}

type Org struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Domains         []string               `protobuf:"bytes,3,rep,name=domains,proto3" json:"domains,omitempty"`
	TwimlAppSid     string                 `protobuf:"bytes,4,opt,name=twiml_app_sid,json=twimlAppSid,proto3" json:"twiml_app_sid,omitempty"`
	TwilioNumber    string                 `protobuf:"bytes,5,opt,name=twilio_number,json=twilioNumber,proto3" json:"twilio_number,omitempty"`
	TwilioNumberSid string                 `protobuf:"bytes,6,opt,name=twilio_number_sid,json=twilioNumberSid,proto3" json:"twilio_number_sid,omitempty"`
	ServiceType     ServiceType            `protobuf:"varint,7,opt,name=service_type,json=serviceType,proto3,enum=hero.orgs.v1.ServiceType" json:"service_type,omitempty"`
	CreatedAt       *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt       *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	TemplateId      string                 `protobuf:"bytes,10,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"` // ID of the template used to setup the org's features
	// Call Forwarding Configuration
	IsCallForwardingEnabled bool   `protobuf:"varint,11,opt,name=is_call_forwarding_enabled,json=isCallForwardingEnabled,proto3" json:"is_call_forwarding_enabled,omitempty"` // Whether call forwarding is enabled for this organization
	PrimaryPhoneNumber      string `protobuf:"bytes,12,opt,name=primary_phone_number,json=primaryPhoneNumber,proto3" json:"primary_phone_number,omitempty"`                   // The primary phone number for the organization (used for Twilio setup and call forwarding)
	CallForwardingType      string `protobuf:"bytes,13,opt,name=call_forwarding_type,json=callForwardingType,proto3" json:"call_forwarding_type,omitempty"`                   // Type of call forwarding: "PSTN" (standard phone) or "SIP" (VoIP)
	SipUri                  string `protobuf:"bytes,14,opt,name=sip_uri,json=sipUri,proto3" json:"sip_uri,omitempty"`                                                         // SIP URI for SIP forwarding (format: sip:<EMAIL>)
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *Org) Reset() {
	*x = Org{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Org) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Org) ProtoMessage() {}

func (x *Org) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Org.ProtoReflect.Descriptor instead.
func (*Org) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{25}
}

func (x *Org) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Org) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Org) GetDomains() []string {
	if x != nil {
		return x.Domains
	}
	return nil
}

func (x *Org) GetTwimlAppSid() string {
	if x != nil {
		return x.TwimlAppSid
	}
	return ""
}

func (x *Org) GetTwilioNumber() string {
	if x != nil {
		return x.TwilioNumber
	}
	return ""
}

func (x *Org) GetTwilioNumberSid() string {
	if x != nil {
		return x.TwilioNumberSid
	}
	return ""
}

func (x *Org) GetServiceType() ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *Org) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Org) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Org) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *Org) GetIsCallForwardingEnabled() bool {
	if x != nil {
		return x.IsCallForwardingEnabled
	}
	return false
}

func (x *Org) GetPrimaryPhoneNumber() string {
	if x != nil {
		return x.PrimaryPhoneNumber
	}
	return ""
}

func (x *Org) GetCallForwardingType() string {
	if x != nil {
		return x.CallForwardingType
	}
	return ""
}

func (x *Org) GetSipUri() string {
	if x != nil {
		return x.SipUri
	}
	return ""
}

type CreateOrgAPIUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrgAPIUserRequest) Reset() {
	*x = CreateOrgAPIUserRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrgAPIUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrgAPIUserRequest) ProtoMessage() {}

func (x *CreateOrgAPIUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrgAPIUserRequest.ProtoReflect.Descriptor instead.
func (*CreateOrgAPIUserRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{26}
}

func (x *CreateOrgAPIUserRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

type CreateOrgAPIUserResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Username          string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	EncryptedPassword string                 `protobuf:"bytes,2,opt,name=encrypted_password,json=encryptedPassword,proto3" json:"encrypted_password,omitempty"`
	HashedPassword    string                 `protobuf:"bytes,3,opt,name=hashed_password,json=hashedPassword,proto3" json:"hashed_password,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateOrgAPIUserResponse) Reset() {
	*x = CreateOrgAPIUserResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrgAPIUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrgAPIUserResponse) ProtoMessage() {}

func (x *CreateOrgAPIUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrgAPIUserResponse.ProtoReflect.Descriptor instead.
func (*CreateOrgAPIUserResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{27}
}

func (x *CreateOrgAPIUserResponse) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *CreateOrgAPIUserResponse) GetEncryptedPassword() string {
	if x != nil {
		return x.EncryptedPassword
	}
	return ""
}

func (x *CreateOrgAPIUserResponse) GetHashedPassword() string {
	if x != nil {
		return x.HashedPassword
	}
	return ""
}

type GetZelloChannelsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetZelloChannelsRequest) Reset() {
	*x = GetZelloChannelsRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetZelloChannelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetZelloChannelsRequest) ProtoMessage() {}

func (x *GetZelloChannelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetZelloChannelsRequest.ProtoReflect.Descriptor instead.
func (*GetZelloChannelsRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{28}
}

type GetZelloChannelsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZelloChannels []*ZelloChannel        `protobuf:"bytes,1,rep,name=zello_channels,json=zelloChannels,proto3" json:"zello_channels,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetZelloChannelsResponse) Reset() {
	*x = GetZelloChannelsResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetZelloChannelsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetZelloChannelsResponse) ProtoMessage() {}

func (x *GetZelloChannelsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetZelloChannelsResponse.ProtoReflect.Descriptor instead.
func (*GetZelloChannelsResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{29}
}

func (x *GetZelloChannelsResponse) GetZelloChannels() []*ZelloChannel {
	if x != nil {
		return x.ZelloChannels
	}
	return nil
}

type ZelloChannel struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	OrgId          int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	ZelloChannelId string                 `protobuf:"bytes,3,opt,name=zello_channel_id,json=zelloChannelId,proto3" json:"zello_channel_id,omitempty"`
	DisplayName    string                 `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ZelloChannel) Reset() {
	*x = ZelloChannel{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ZelloChannel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZelloChannel) ProtoMessage() {}

func (x *ZelloChannel) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZelloChannel.ProtoReflect.Descriptor instead.
func (*ZelloChannel) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{30}
}

func (x *ZelloChannel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ZelloChannel) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *ZelloChannel) GetZelloChannelId() string {
	if x != nil {
		return x.ZelloChannelId
	}
	return ""
}

func (x *ZelloChannel) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

type CreateCognitoUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCognitoUserRequest) Reset() {
	*x = CreateCognitoUserRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCognitoUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCognitoUserRequest) ProtoMessage() {}

func (x *CreateCognitoUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCognitoUserRequest.ProtoReflect.Descriptor instead.
func (*CreateCognitoUserRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{31}
}

func (x *CreateCognitoUserRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *CreateCognitoUserRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *CreateCognitoUserRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateCognitoUserRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type CreateCognitoUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	CognitoSubId  string                 `protobuf:"bytes,2,opt,name=cognito_sub_id,json=cognitoSubId,proto3" json:"cognito_sub_id,omitempty"` // The Cognito user ID (sub)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCognitoUserResponse) Reset() {
	*x = CreateCognitoUserResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCognitoUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCognitoUserResponse) ProtoMessage() {}

func (x *CreateCognitoUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCognitoUserResponse.ProtoReflect.Descriptor instead.
func (*CreateCognitoUserResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{32}
}

func (x *CreateCognitoUserResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateCognitoUserResponse) GetCognitoSubId() string {
	if x != nil {
		return x.CognitoSubId
	}
	return ""
}

// Organization Contact Book Messages
type ContactRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	OrgId         int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Phone         string                 `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContactRecord) Reset() {
	*x = ContactRecord{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactRecord) ProtoMessage() {}

func (x *ContactRecord) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactRecord.ProtoReflect.Descriptor instead.
func (*ContactRecord) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{33}
}

func (x *ContactRecord) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ContactRecord) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *ContactRecord) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ContactRecord) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ContactRecord) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ContactRecord) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type AddToContactBookRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Phone         string                 `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddToContactBookRequest) Reset() {
	*x = AddToContactBookRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddToContactBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddToContactBookRequest) ProtoMessage() {}

func (x *AddToContactBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddToContactBookRequest.ProtoReflect.Descriptor instead.
func (*AddToContactBookRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{34}
}

func (x *AddToContactBookRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *AddToContactBookRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddToContactBookRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type AddToContactBookResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Contact       *ContactRecord         `protobuf:"bytes,1,opt,name=contact,proto3" json:"contact,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddToContactBookResponse) Reset() {
	*x = AddToContactBookResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddToContactBookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddToContactBookResponse) ProtoMessage() {}

func (x *AddToContactBookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddToContactBookResponse.ProtoReflect.Descriptor instead.
func (*AddToContactBookResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{35}
}

func (x *AddToContactBookResponse) GetContact() *ContactRecord {
	if x != nil {
		return x.Contact
	}
	return nil
}

type UpdateContactInContactBookRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Phone         string                 `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateContactInContactBookRequest) Reset() {
	*x = UpdateContactInContactBookRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContactInContactBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContactInContactBookRequest) ProtoMessage() {}

func (x *UpdateContactInContactBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContactInContactBookRequest.ProtoReflect.Descriptor instead.
func (*UpdateContactInContactBookRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{36}
}

func (x *UpdateContactInContactBookRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateContactInContactBookRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateContactInContactBookRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type UpdateContactInContactBookResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Contact       *ContactRecord         `protobuf:"bytes,1,opt,name=contact,proto3" json:"contact,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateContactInContactBookResponse) Reset() {
	*x = UpdateContactInContactBookResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContactInContactBookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContactInContactBookResponse) ProtoMessage() {}

func (x *UpdateContactInContactBookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContactInContactBookResponse.ProtoReflect.Descriptor instead.
func (*UpdateContactInContactBookResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{37}
}

func (x *UpdateContactInContactBookResponse) GetContact() *ContactRecord {
	if x != nil {
		return x.Contact
	}
	return nil
}

type DeleteFromContactBookRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteFromContactBookRequest) Reset() {
	*x = DeleteFromContactBookRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFromContactBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFromContactBookRequest) ProtoMessage() {}

func (x *DeleteFromContactBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFromContactBookRequest.ProtoReflect.Descriptor instead.
func (*DeleteFromContactBookRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{38}
}

func (x *DeleteFromContactBookRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteFromContactBookResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteFromContactBookResponse) Reset() {
	*x = DeleteFromContactBookResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFromContactBookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFromContactBookResponse) ProtoMessage() {}

func (x *DeleteFromContactBookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFromContactBookResponse.ProtoReflect.Descriptor instead.
func (*DeleteFromContactBookResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{39}
}

type GetContactFromContactBookRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactFromContactBookRequest) Reset() {
	*x = GetContactFromContactBookRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactFromContactBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactFromContactBookRequest) ProtoMessage() {}

func (x *GetContactFromContactBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactFromContactBookRequest.ProtoReflect.Descriptor instead.
func (*GetContactFromContactBookRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{40}
}

func (x *GetContactFromContactBookRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetContactFromContactBookResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Contact       *ContactRecord         `protobuf:"bytes,1,opt,name=contact,proto3" json:"contact,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactFromContactBookResponse) Reset() {
	*x = GetContactFromContactBookResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactFromContactBookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactFromContactBookResponse) ProtoMessage() {}

func (x *GetContactFromContactBookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactFromContactBookResponse.ProtoReflect.Descriptor instead.
func (*GetContactFromContactBookResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{41}
}

func (x *GetContactFromContactBookResponse) GetContact() *ContactRecord {
	if x != nil {
		return x.Contact
	}
	return nil
}

type ListContactsInContactBookRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	PageToken     string                 `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"` // Token for pagination (empty for first page)
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`   // Number of contacts to return (max 100)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactsInContactBookRequest) Reset() {
	*x = ListContactsInContactBookRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactsInContactBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactsInContactBookRequest) ProtoMessage() {}

func (x *ListContactsInContactBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactsInContactBookRequest.ProtoReflect.Descriptor instead.
func (*ListContactsInContactBookRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{42}
}

func (x *ListContactsInContactBookRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *ListContactsInContactBookRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListContactsInContactBookRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListContactsInContactBookResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Contacts      []*ContactRecord       `protobuf:"bytes,1,rep,name=contacts,proto3" json:"contacts,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"` // Token for next page (empty if no more pages)
	TotalCount    int32                  `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`           // Total number of contacts in the contact book
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactsInContactBookResponse) Reset() {
	*x = ListContactsInContactBookResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactsInContactBookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactsInContactBookResponse) ProtoMessage() {}

func (x *ListContactsInContactBookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactsInContactBookResponse.ProtoReflect.Descriptor instead.
func (*ListContactsInContactBookResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{43}
}

func (x *ListContactsInContactBookResponse) GetContacts() []*ContactRecord {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *ListContactsInContactBookResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListContactsInContactBookResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type GetContactByPhoneNumberRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	Phone         string                 `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactByPhoneNumberRequest) Reset() {
	*x = GetContactByPhoneNumberRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactByPhoneNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactByPhoneNumberRequest) ProtoMessage() {}

func (x *GetContactByPhoneNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactByPhoneNumberRequest.ProtoReflect.Descriptor instead.
func (*GetContactByPhoneNumberRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{44}
}

func (x *GetContactByPhoneNumberRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *GetContactByPhoneNumberRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type GetContactByPhoneNumberResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Contact       *ContactRecord         `protobuf:"bytes,1,opt,name=contact,proto3" json:"contact,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactByPhoneNumberResponse) Reset() {
	*x = GetContactByPhoneNumberResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactByPhoneNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactByPhoneNumberResponse) ProtoMessage() {}

func (x *GetContactByPhoneNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactByPhoneNumberResponse.ProtoReflect.Descriptor instead.
func (*GetContactByPhoneNumberResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{45}
}

func (x *GetContactByPhoneNumberResponse) GetContact() *ContactRecord {
	if x != nil {
		return x.Contact
	}
	return nil
}

// Pre-registration user mapping messages
type PreRegistrationUserMapping struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	OrgId         int32                  `protobuf:"varint,3,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	RoleName      string                 `protobuf:"bytes,4,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	AssetType     v2.AssetType           `protobuf:"varint,5,opt,name=asset_type,json=assetType,proto3,enum=hero.assets.v2.AssetType" json:"asset_type,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UsedAt        *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=used_at,json=usedAt,proto3" json:"used_at,omitempty"`
	CreatedBy     string                 `protobuf:"bytes,8,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PreRegistrationUserMapping) Reset() {
	*x = PreRegistrationUserMapping{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PreRegistrationUserMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreRegistrationUserMapping) ProtoMessage() {}

func (x *PreRegistrationUserMapping) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreRegistrationUserMapping.ProtoReflect.Descriptor instead.
func (*PreRegistrationUserMapping) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{46}
}

func (x *PreRegistrationUserMapping) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PreRegistrationUserMapping) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *PreRegistrationUserMapping) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *PreRegistrationUserMapping) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

func (x *PreRegistrationUserMapping) GetAssetType() v2.AssetType {
	if x != nil {
		return x.AssetType
	}
	return v2.AssetType(0)
}

func (x *PreRegistrationUserMapping) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PreRegistrationUserMapping) GetUsedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UsedAt
	}
	return nil
}

func (x *PreRegistrationUserMapping) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type CreatePreRegistrationMappingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	OrgId         int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	RoleName      string                 `protobuf:"bytes,3,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	AssetType     v2.AssetType           `protobuf:"varint,4,opt,name=asset_type,json=assetType,proto3,enum=hero.assets.v2.AssetType" json:"asset_type,omitempty"` // Optional, defaults to RESPONDER
	CreatedBy     string                 `protobuf:"bytes,5,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePreRegistrationMappingRequest) Reset() {
	*x = CreatePreRegistrationMappingRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePreRegistrationMappingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePreRegistrationMappingRequest) ProtoMessage() {}

func (x *CreatePreRegistrationMappingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePreRegistrationMappingRequest.ProtoReflect.Descriptor instead.
func (*CreatePreRegistrationMappingRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{47}
}

func (x *CreatePreRegistrationMappingRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreatePreRegistrationMappingRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *CreatePreRegistrationMappingRequest) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

func (x *CreatePreRegistrationMappingRequest) GetAssetType() v2.AssetType {
	if x != nil {
		return x.AssetType
	}
	return v2.AssetType(0)
}

func (x *CreatePreRegistrationMappingRequest) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type CreatePreRegistrationMappingResponse struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Mapping       *PreRegistrationUserMapping `protobuf:"bytes,1,opt,name=mapping,proto3" json:"mapping,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePreRegistrationMappingResponse) Reset() {
	*x = CreatePreRegistrationMappingResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePreRegistrationMappingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePreRegistrationMappingResponse) ProtoMessage() {}

func (x *CreatePreRegistrationMappingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePreRegistrationMappingResponse.ProtoReflect.Descriptor instead.
func (*CreatePreRegistrationMappingResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{48}
}

func (x *CreatePreRegistrationMappingResponse) GetMapping() *PreRegistrationUserMapping {
	if x != nil {
		return x.Mapping
	}
	return nil
}

type CreatePreRegistrationMappingsRequest struct {
	state         protoimpl.MessageState                 `protogen:"open.v1"`
	Mappings      []*CreatePreRegistrationMappingRequest `protobuf:"bytes,1,rep,name=mappings,proto3" json:"mappings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePreRegistrationMappingsRequest) Reset() {
	*x = CreatePreRegistrationMappingsRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePreRegistrationMappingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePreRegistrationMappingsRequest) ProtoMessage() {}

func (x *CreatePreRegistrationMappingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePreRegistrationMappingsRequest.ProtoReflect.Descriptor instead.
func (*CreatePreRegistrationMappingsRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{49}
}

func (x *CreatePreRegistrationMappingsRequest) GetMappings() []*CreatePreRegistrationMappingRequest {
	if x != nil {
		return x.Mappings
	}
	return nil
}

type CreatePreRegistrationMappingsResponse struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Mappings      []*PreRegistrationUserMapping `protobuf:"bytes,1,rep,name=mappings,proto3" json:"mappings,omitempty"`
	Errors        []string                      `protobuf:"bytes,2,rep,name=errors,proto3" json:"errors,omitempty"` // Error messages for failed mappings
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePreRegistrationMappingsResponse) Reset() {
	*x = CreatePreRegistrationMappingsResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePreRegistrationMappingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePreRegistrationMappingsResponse) ProtoMessage() {}

func (x *CreatePreRegistrationMappingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePreRegistrationMappingsResponse.ProtoReflect.Descriptor instead.
func (*CreatePreRegistrationMappingsResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{50}
}

func (x *CreatePreRegistrationMappingsResponse) GetMappings() []*PreRegistrationUserMapping {
	if x != nil {
		return x.Mappings
	}
	return nil
}

func (x *CreatePreRegistrationMappingsResponse) GetErrors() []string {
	if x != nil {
		return x.Errors
	}
	return nil
}

type GetPreRegistrationMappingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	OrgId         int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPreRegistrationMappingRequest) Reset() {
	*x = GetPreRegistrationMappingRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPreRegistrationMappingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPreRegistrationMappingRequest) ProtoMessage() {}

func (x *GetPreRegistrationMappingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPreRegistrationMappingRequest.ProtoReflect.Descriptor instead.
func (*GetPreRegistrationMappingRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{51}
}

func (x *GetPreRegistrationMappingRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetPreRegistrationMappingRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

type GetPreRegistrationMappingResponse struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Mapping       *PreRegistrationUserMapping `protobuf:"bytes,1,opt,name=mapping,proto3" json:"mapping,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPreRegistrationMappingResponse) Reset() {
	*x = GetPreRegistrationMappingResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPreRegistrationMappingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPreRegistrationMappingResponse) ProtoMessage() {}

func (x *GetPreRegistrationMappingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPreRegistrationMappingResponse.ProtoReflect.Descriptor instead.
func (*GetPreRegistrationMappingResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{52}
}

func (x *GetPreRegistrationMappingResponse) GetMapping() *PreRegistrationUserMapping {
	if x != nil {
		return x.Mapping
	}
	return nil
}

type ListPreRegistrationMappingsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	PageToken     string                 `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	IncludeUsed   bool                   `protobuf:"varint,4,opt,name=include_used,json=includeUsed,proto3" json:"include_used,omitempty"` // Whether to include already used mappings
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPreRegistrationMappingsRequest) Reset() {
	*x = ListPreRegistrationMappingsRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPreRegistrationMappingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPreRegistrationMappingsRequest) ProtoMessage() {}

func (x *ListPreRegistrationMappingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPreRegistrationMappingsRequest.ProtoReflect.Descriptor instead.
func (*ListPreRegistrationMappingsRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{53}
}

func (x *ListPreRegistrationMappingsRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *ListPreRegistrationMappingsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListPreRegistrationMappingsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListPreRegistrationMappingsRequest) GetIncludeUsed() bool {
	if x != nil {
		return x.IncludeUsed
	}
	return false
}

type ListPreRegistrationMappingsResponse struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Mappings      []*PreRegistrationUserMapping `protobuf:"bytes,1,rep,name=mappings,proto3" json:"mappings,omitempty"`
	NextPageToken string                        `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	TotalCount    int32                         `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPreRegistrationMappingsResponse) Reset() {
	*x = ListPreRegistrationMappingsResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPreRegistrationMappingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPreRegistrationMappingsResponse) ProtoMessage() {}

func (x *ListPreRegistrationMappingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPreRegistrationMappingsResponse.ProtoReflect.Descriptor instead.
func (*ListPreRegistrationMappingsResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{54}
}

func (x *ListPreRegistrationMappingsResponse) GetMappings() []*PreRegistrationUserMapping {
	if x != nil {
		return x.Mappings
	}
	return nil
}

func (x *ListPreRegistrationMappingsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListPreRegistrationMappingsResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type UpdatePreRegistrationMappingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	RoleName      string                 `protobuf:"bytes,2,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	AssetType     v2.AssetType           `protobuf:"varint,3,opt,name=asset_type,json=assetType,proto3,enum=hero.assets.v2.AssetType" json:"asset_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePreRegistrationMappingRequest) Reset() {
	*x = UpdatePreRegistrationMappingRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePreRegistrationMappingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePreRegistrationMappingRequest) ProtoMessage() {}

func (x *UpdatePreRegistrationMappingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePreRegistrationMappingRequest.ProtoReflect.Descriptor instead.
func (*UpdatePreRegistrationMappingRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{55}
}

func (x *UpdatePreRegistrationMappingRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdatePreRegistrationMappingRequest) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

func (x *UpdatePreRegistrationMappingRequest) GetAssetType() v2.AssetType {
	if x != nil {
		return x.AssetType
	}
	return v2.AssetType(0)
}

type UpdatePreRegistrationMappingResponse struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Mapping       *PreRegistrationUserMapping `protobuf:"bytes,1,opt,name=mapping,proto3" json:"mapping,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePreRegistrationMappingResponse) Reset() {
	*x = UpdatePreRegistrationMappingResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePreRegistrationMappingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePreRegistrationMappingResponse) ProtoMessage() {}

func (x *UpdatePreRegistrationMappingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePreRegistrationMappingResponse.ProtoReflect.Descriptor instead.
func (*UpdatePreRegistrationMappingResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{56}
}

func (x *UpdatePreRegistrationMappingResponse) GetMapping() *PreRegistrationUserMapping {
	if x != nil {
		return x.Mapping
	}
	return nil
}

type DeletePreRegistrationMappingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePreRegistrationMappingRequest) Reset() {
	*x = DeletePreRegistrationMappingRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePreRegistrationMappingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePreRegistrationMappingRequest) ProtoMessage() {}

func (x *DeletePreRegistrationMappingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePreRegistrationMappingRequest.ProtoReflect.Descriptor instead.
func (*DeletePreRegistrationMappingRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{57}
}

func (x *DeletePreRegistrationMappingRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeletePreRegistrationMappingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePreRegistrationMappingResponse) Reset() {
	*x = DeletePreRegistrationMappingResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePreRegistrationMappingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePreRegistrationMappingResponse) ProtoMessage() {}

func (x *DeletePreRegistrationMappingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePreRegistrationMappingResponse.ProtoReflect.Descriptor instead.
func (*DeletePreRegistrationMappingResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{58}
}

type MarkMappingAsUsedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MappingId     string                 `protobuf:"bytes,1,opt,name=mapping_id,json=mappingId,proto3" json:"mapping_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkMappingAsUsedRequest) Reset() {
	*x = MarkMappingAsUsedRequest{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkMappingAsUsedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkMappingAsUsedRequest) ProtoMessage() {}

func (x *MarkMappingAsUsedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkMappingAsUsedRequest.ProtoReflect.Descriptor instead.
func (*MarkMappingAsUsedRequest) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{59}
}

func (x *MarkMappingAsUsedRequest) GetMappingId() string {
	if x != nil {
		return x.MappingId
	}
	return ""
}

type MarkMappingAsUsedResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkMappingAsUsedResponse) Reset() {
	*x = MarkMappingAsUsedResponse{}
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkMappingAsUsedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkMappingAsUsedResponse) ProtoMessage() {}

func (x *MarkMappingAsUsedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orgs_v1_orgs_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkMappingAsUsedResponse.ProtoReflect.Descriptor instead.
func (*MarkMappingAsUsedResponse) Descriptor() ([]byte, []int) {
	return file_hero_orgs_v1_orgs_proto_rawDescGZIP(), []int{60}
}

func (x *MarkMappingAsUsedResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_hero_orgs_v1_orgs_proto protoreflect.FileDescriptor

var file_hero_orgs_v1_orgs_proto_rawDesc = []byte{
	0x0a, 0x17, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x6f, 0x72, 0x67, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6f,
	0x72, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x76, 0x32, 0x2f,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2f, 0x0a, 0x16,
	0x54, 0x75, 0x72, 0x6e, 0x4f, 0x6e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x22, 0x33, 0x0a,
	0x17, 0x54, 0x75, 0x72, 0x6e, 0x4f, 0x6e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x22, 0x30, 0x0a, 0x17, 0x54, 0x75, 0x72, 0x6e, 0x4f, 0x66, 0x66, 0x47, 0x75, 0x65,
	0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a,
	0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f,
	0x72, 0x67, 0x49, 0x64, 0x22, 0x34, 0x0a, 0x18, 0x54, 0x75, 0x72, 0x6e, 0x4f, 0x66, 0x66, 0x47,
	0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x3a, 0x0a, 0x1f, 0x47, 0x65,
	0x74, 0x4f, 0x72, 0x67, 0x41, 0x50, 0x49, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x65, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x67,
	0x41, 0x50, 0x49, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x42, 0x79,
	0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x0c, 0x6f, 0x72,
	0x67, 0x5f, 0x61, 0x70, 0x69, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x72, 0x67, 0x41, 0x70, 0x69, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x52, 0x0a, 0x6f, 0x72, 0x67, 0x41, 0x70, 0x69, 0x55, 0x73, 0x65, 0x72, 0x22, 0x37, 0x0a,
	0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x23, 0x0a, 0x03, 0x6f, 0x72, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x67, 0x52, 0x03, 0x6f, 0x72, 0x67, 0x22, 0x38, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x03, 0x6f,
	0x72, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x52, 0x03, 0x6f, 0x72, 0x67,
	0x22, 0x37, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x03, 0x6f, 0x72, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x72, 0x67, 0x52, 0x03, 0x6f, 0x72, 0x67, 0x22, 0x38, 0x0a, 0x11, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x03, 0x6f, 0x72, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x52, 0x03,
	0x6f, 0x72, 0x67, 0x22, 0x22, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x4f, 0x72, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1f, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x35, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x03, 0x6f, 0x72, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x52,
	0x03, 0x6f, 0x72, 0x67, 0x22, 0x11, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x39, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4f,
	0x72, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x6f,
	0x72, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x52, 0x04, 0x6f, 0x72,
	0x67, 0x73, 0x22, 0x51, 0x0a, 0x17, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x67, 0x43, 0x72, 0x65, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x6c, 0x0a, 0x18, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x67, 0x43, 0x72, 0x65, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x3a, 0x0a, 0x0c, 0x6f, 0x72, 0x67, 0x5f, 0x61,
	0x70, 0x69, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67,
	0x41, 0x70, 0x69, 0x55, 0x73, 0x65, 0x72, 0x52, 0x0a, 0x6f, 0x72, 0x67, 0x41, 0x70, 0x69, 0x55,
	0x73, 0x65, 0x72, 0x22, 0x9d, 0x02, 0x0a, 0x0a, 0x4f, 0x72, 0x67, 0x41, 0x70, 0x69, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x65, 0x64, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x68,
	0x61, 0x73, 0x68, 0x65, 0x64, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x22, 0xc7, 0x02, 0x0a, 0x11, 0x4f, 0x72, 0x67, 0x41, 0x70, 0x69, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x12,
	0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x65, 0x64, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x68,
	0x61, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x61, 0x77, 0x5f, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x50,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x98, 0x02,
	0x0a, 0x08, 0x4f, 0x72, 0x67, 0x51, 0x75, 0x65, 0x75, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x72,
	0x69, 0x65, 0x6e, 0x64, 0x6c, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x6c, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x28, 0x0a, 0x10, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f,
	0x73, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x77, 0x69, 0x6c, 0x69,
	0x6f, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x22, 0x9f, 0x01, 0x0a, 0x15, 0x49, 0x6e, 0x73,
	0x65, 0x72, 0x74, 0x4f, 0x72, 0x67, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x6c, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x72, 0x69, 0x65, 0x6e,
	0x64, 0x6c, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x77, 0x69, 0x6c, 0x69,
	0x6f, 0x5f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x69,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x22, 0x4d, 0x0a, 0x16, 0x49, 0x6e,
	0x73, 0x65, 0x72, 0x74, 0x4f, 0x72, 0x67, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x09, 0x6f, 0x72, 0x67, 0x5f, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f,
	0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52,
	0x08, 0x6f, 0x72, 0x67, 0x51, 0x75, 0x65, 0x75, 0x65, 0x22, 0x59, 0x0a, 0x1b, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x51, 0x75, 0x65, 0x75,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x6c, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x6c, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x3b, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x67, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x73, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x75, 0x65, 0x53, 0x69,
	0x64, 0x22, 0xc7, 0x04, 0x0a, 0x03, 0x4f, 0x72, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x74, 0x77, 0x69, 0x6d, 0x6c,
	0x5f, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x74, 0x77, 0x69, 0x6d, 0x6c, 0x41, 0x70, 0x70, 0x53, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74,
	0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x2a, 0x0a, 0x11, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x77, 0x69,
	0x6c, 0x69, 0x6f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x53, 0x69, 0x64, 0x12, 0x3c, 0x0a, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x66, 0x6f, 0x72,
	0x77, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x69, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x46, 0x6f, 0x72,
	0x77, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x30,
	0x0a, 0x14, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x30, 0x0a, 0x14, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x63, 0x61, 0x6c, 0x6c, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x69, 0x70, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x69, 0x70, 0x55, 0x72, 0x69, 0x22, 0x30, 0x0a, 0x17, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x41, 0x50, 0x49, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x22, 0x8e, 0x01,
	0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x41, 0x50, 0x49, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x65, 0x64, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x50, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x5f,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x19,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x5a, 0x65, 0x6c, 0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x5d, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x5a, 0x65, 0x6c, 0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x7a, 0x65, 0x6c, 0x6c, 0x6f, 0x5f, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x5a, 0x65, 0x6c,
	0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x0d, 0x7a, 0x65, 0x6c, 0x6c, 0x6f,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x22, 0x82, 0x01, 0x0a, 0x0c, 0x5a, 0x65, 0x6c,
	0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64,
	0x12, 0x28, 0x0a, 0x10, 0x7a, 0x65, 0x6c, 0x6c, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x7a, 0x65, 0x6c, 0x6c,
	0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x7f, 0x0a,
	0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x6f, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x5b,
	0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x6f, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x6f,
	0x5f, 0x73, 0x75, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x6f, 0x67, 0x6e, 0x69, 0x74, 0x6f, 0x53, 0x75, 0x62, 0x49, 0x64, 0x22, 0xd6, 0x01, 0x0a, 0x0d,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f,
	0x72, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0x5a, 0x0a, 0x17, 0x41, 0x64, 0x64, 0x54, 0x6f, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x22, 0x51, 0x0a, 0x18, 0x41, 0x64, 0x64, 0x54, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x22, 0x5d, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x22, 0x5b, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x49, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x22,
	0x2e, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x1f, 0x0a, 0x1d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x32, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x46, 0x72,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x5a, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x22, 0x75, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73,
	0x49, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xa5, 0x01, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x49, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a,
	0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x08, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x4d, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x79, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x22, 0x58,
	0x0a, 0x1f, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x79, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x35, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x22, 0xbf, 0x02, 0x0a, 0x1a, 0x50, 0x72, 0x65,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72,
	0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x15, 0x0a,
	0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f,
	0x72, 0x67, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x38, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x06, 0x75, 0x73, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x22, 0xc8, 0x01, 0x0a, 0x23, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0a,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x42, 0x79, 0x22, 0x6a, 0x0a, 0x24, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a,
	0x07, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65,
	0x72, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x22, 0x75, 0x0a, 0x24, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4d, 0x0a, 0x08, 0x6d, 0x61, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x08,
	0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x85, 0x01, 0x0a, 0x25, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x44, 0x0a, 0x08, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x08,
	0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x22, 0x4f, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49,
	0x64, 0x22, 0x67, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f,
	0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x22, 0x9a, 0x01, 0x0a, 0x22, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f,
	0x75, 0x73, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x55, 0x73, 0x65, 0x64, 0x22, 0xb4, 0x01, 0x0a, 0x23, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x44, 0x0a, 0x08, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x55, 0x73, 0x65, 0x72, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x08, 0x6d, 0x61, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x8c,
	0x01, 0x0a, 0x23, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x6a, 0x0a,
	0x24, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72,
	0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x52, 0x07, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x22, 0x35, 0x0a, 0x23, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x26, 0x0a, 0x24, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x39, 0x0a, 0x18, 0x4d, 0x61, 0x72, 0x6b,
	0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x55, 0x73, 0x65, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x49, 0x64, 0x22, 0x35, 0x0a, 0x19, 0x4d, 0x61, 0x72, 0x6b, 0x4d, 0x61, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x41, 0x73, 0x55, 0x73, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2a, 0x5f, 0x0a, 0x0b, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x45, 0x52,
	0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x45, 0x52, 0x56, 0x49,
	0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x4d, 0x4f, 0x10, 0x01, 0x12, 0x1b,
	0x0a, 0x17, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x32, 0xb3, 0x17, 0x0a, 0x0b,
	0x4f, 0x72, 0x67, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4e, 0x0a, 0x09, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x12, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x09, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x12, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x09, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x12, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x06, 0x47,
	0x65, 0x74, 0x4f, 0x72, 0x67, 0x12, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x4b, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x73, 0x12, 0x1d,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4f, 0x72, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x72, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x63, 0x0a, 0x10, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x43, 0x72,
	0x65, 0x64, 0x73, 0x12, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x43, 0x72,
	0x65, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x4f, 0x72, 0x67, 0x43, 0x72, 0x65, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x67, 0x41, 0x50, 0x49, 0x55, 0x73, 0x65, 0x72, 0x12, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x67, 0x41, 0x50, 0x49, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x41, 0x50, 0x49, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7b, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x4f, 0x72, 0x67, 0x41, 0x50, 0x49, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x42, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x41, 0x50, 0x49, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x41, 0x50, 0x49, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x5a, 0x65, 0x6c,
	0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12, 0x25, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x5a, 0x65, 0x6c,
	0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x5a, 0x65, 0x6c, 0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0e, 0x49,
	0x6e, 0x73, 0x65, 0x72, 0x74, 0x4f, 0x72, 0x67, 0x51, 0x75, 0x65, 0x75, 0x65, 0x12, 0x23, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73,
	0x65, 0x72, 0x74, 0x4f, 0x72, 0x67, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x24, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x4f, 0x72, 0x67, 0x51, 0x75, 0x65, 0x75, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x14, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x51, 0x75, 0x65,
	0x75, 0x65, 0x12, 0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x54, 0x77, 0x69, 0x6c, 0x69,
	0x6f, 0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x67, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x51, 0x75, 0x65, 0x75,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x11, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x6f, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x6f, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x67, 0x6e, 0x69, 0x74, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x54, 0x6f, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f,
	0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x6f, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64,
	0x64, 0x54, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x81, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x2f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f,
	0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x6f,
	0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x72, 0x0a, 0x15,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x2a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x7e, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x46, 0x72,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x2e, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x7e, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73,
	0x49, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x2e, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x49, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x49, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x78, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x79,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x0f, 0x54, 0x75,
	0x72, 0x6e, 0x4f, 0x6e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x75, 0x72,
	0x6e, 0x4f, 0x6e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x75, 0x72, 0x6e, 0x4f, 0x6e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x10,
	0x54, 0x75, 0x72, 0x6e, 0x4f, 0x66, 0x66, 0x47, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x12, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x75, 0x72, 0x6e, 0x4f, 0x66, 0x66, 0x47, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f,
	0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x75, 0x72, 0x6e, 0x4f, 0x66, 0x66, 0x47, 0x75,
	0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x06, 0x9a, 0xb5, 0x18, 0x02, 0x08, 0x01, 0x12, 0x87, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x31, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x8a, 0x01, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x32, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f,
	0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7e,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x2e, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x84,
	0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x30,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x31, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x31, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72,
	0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x87, 0x01, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x12, 0x31, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x11, 0x4d, 0x61, 0x72,
	0x6b, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x55, 0x73, 0x65, 0x64, 0x12, 0x26,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61,
	0x72, 0x6b, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x55, 0x73, 0x65, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72,
	0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x41, 0x73, 0x55, 0x73, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x42, 0x19, 0x5a, 0x17, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x65, 0x72, 0x6f, 0x2f,
	0x6f, 0x72, 0x67, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hero_orgs_v1_orgs_proto_rawDescOnce sync.Once
	file_hero_orgs_v1_orgs_proto_rawDescData = file_hero_orgs_v1_orgs_proto_rawDesc
)

func file_hero_orgs_v1_orgs_proto_rawDescGZIP() []byte {
	file_hero_orgs_v1_orgs_proto_rawDescOnce.Do(func() {
		file_hero_orgs_v1_orgs_proto_rawDescData = protoimpl.X.CompressGZIP(file_hero_orgs_v1_orgs_proto_rawDescData)
	})
	return file_hero_orgs_v1_orgs_proto_rawDescData
}

var file_hero_orgs_v1_orgs_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_hero_orgs_v1_orgs_proto_msgTypes = make([]protoimpl.MessageInfo, 61)
var file_hero_orgs_v1_orgs_proto_goTypes = []any{
	(ServiceType)(0),                              // 0: hero.orgs.v1.ServiceType
	(*TurnOnGuestModeRequest)(nil),                // 1: hero.orgs.v1.TurnOnGuestModeRequest
	(*TurnOnGuestModeResponse)(nil),               // 2: hero.orgs.v1.TurnOnGuestModeResponse
	(*TurnOffGuestModeRequest)(nil),               // 3: hero.orgs.v1.TurnOffGuestModeRequest
	(*TurnOffGuestModeResponse)(nil),              // 4: hero.orgs.v1.TurnOffGuestModeResponse
	(*GetOrgAPIUserPrivateByIdRequest)(nil),       // 5: hero.orgs.v1.GetOrgAPIUserPrivateByIdRequest
	(*GetOrgAPIUserPrivateByIdResponse)(nil),      // 6: hero.orgs.v1.GetOrgAPIUserPrivateByIdResponse
	(*CreateOrgRequest)(nil),                      // 7: hero.orgs.v1.CreateOrgRequest
	(*CreateOrgResponse)(nil),                     // 8: hero.orgs.v1.CreateOrgResponse
	(*UpdateOrgRequest)(nil),                      // 9: hero.orgs.v1.UpdateOrgRequest
	(*UpdateOrgResponse)(nil),                     // 10: hero.orgs.v1.UpdateOrgResponse
	(*DeleteOrgRequest)(nil),                      // 11: hero.orgs.v1.DeleteOrgRequest
	(*DeleteOrgResponse)(nil),                     // 12: hero.orgs.v1.DeleteOrgResponse
	(*GetOrgRequest)(nil),                         // 13: hero.orgs.v1.GetOrgRequest
	(*GetOrgResponse)(nil),                        // 14: hero.orgs.v1.GetOrgResponse
	(*ListOrgsRequest)(nil),                       // 15: hero.orgs.v1.ListOrgsRequest
	(*ListOrgsResponse)(nil),                      // 16: hero.orgs.v1.ListOrgsResponse
	(*ValidateOrgCredsRequest)(nil),               // 17: hero.orgs.v1.ValidateOrgCredsRequest
	(*ValidateOrgCredsResponse)(nil),              // 18: hero.orgs.v1.ValidateOrgCredsResponse
	(*OrgApiUser)(nil),                            // 19: hero.orgs.v1.OrgApiUser
	(*OrgApiUserPrivate)(nil),                     // 20: hero.orgs.v1.OrgApiUserPrivate
	(*OrgQueue)(nil),                              // 21: hero.orgs.v1.OrgQueue
	(*InsertOrgQueueRequest)(nil),                 // 22: hero.orgs.v1.InsertOrgQueueRequest
	(*InsertOrgQueueResponse)(nil),                // 23: hero.orgs.v1.InsertOrgQueueResponse
	(*CreateOrgTwilioQueueRequest)(nil),           // 24: hero.orgs.v1.CreateOrgTwilioQueueRequest
	(*CreateOrgTwilioQueueResponse)(nil),          // 25: hero.orgs.v1.CreateOrgTwilioQueueResponse
	(*Org)(nil),                                   // 26: hero.orgs.v1.Org
	(*CreateOrgAPIUserRequest)(nil),               // 27: hero.orgs.v1.CreateOrgAPIUserRequest
	(*CreateOrgAPIUserResponse)(nil),              // 28: hero.orgs.v1.CreateOrgAPIUserResponse
	(*GetZelloChannelsRequest)(nil),               // 29: hero.orgs.v1.GetZelloChannelsRequest
	(*GetZelloChannelsResponse)(nil),              // 30: hero.orgs.v1.GetZelloChannelsResponse
	(*ZelloChannel)(nil),                          // 31: hero.orgs.v1.ZelloChannel
	(*CreateCognitoUserRequest)(nil),              // 32: hero.orgs.v1.CreateCognitoUserRequest
	(*CreateCognitoUserResponse)(nil),             // 33: hero.orgs.v1.CreateCognitoUserResponse
	(*ContactRecord)(nil),                         // 34: hero.orgs.v1.ContactRecord
	(*AddToContactBookRequest)(nil),               // 35: hero.orgs.v1.AddToContactBookRequest
	(*AddToContactBookResponse)(nil),              // 36: hero.orgs.v1.AddToContactBookResponse
	(*UpdateContactInContactBookRequest)(nil),     // 37: hero.orgs.v1.UpdateContactInContactBookRequest
	(*UpdateContactInContactBookResponse)(nil),    // 38: hero.orgs.v1.UpdateContactInContactBookResponse
	(*DeleteFromContactBookRequest)(nil),          // 39: hero.orgs.v1.DeleteFromContactBookRequest
	(*DeleteFromContactBookResponse)(nil),         // 40: hero.orgs.v1.DeleteFromContactBookResponse
	(*GetContactFromContactBookRequest)(nil),      // 41: hero.orgs.v1.GetContactFromContactBookRequest
	(*GetContactFromContactBookResponse)(nil),     // 42: hero.orgs.v1.GetContactFromContactBookResponse
	(*ListContactsInContactBookRequest)(nil),      // 43: hero.orgs.v1.ListContactsInContactBookRequest
	(*ListContactsInContactBookResponse)(nil),     // 44: hero.orgs.v1.ListContactsInContactBookResponse
	(*GetContactByPhoneNumberRequest)(nil),        // 45: hero.orgs.v1.GetContactByPhoneNumberRequest
	(*GetContactByPhoneNumberResponse)(nil),       // 46: hero.orgs.v1.GetContactByPhoneNumberResponse
	(*PreRegistrationUserMapping)(nil),            // 47: hero.orgs.v1.PreRegistrationUserMapping
	(*CreatePreRegistrationMappingRequest)(nil),   // 48: hero.orgs.v1.CreatePreRegistrationMappingRequest
	(*CreatePreRegistrationMappingResponse)(nil),  // 49: hero.orgs.v1.CreatePreRegistrationMappingResponse
	(*CreatePreRegistrationMappingsRequest)(nil),  // 50: hero.orgs.v1.CreatePreRegistrationMappingsRequest
	(*CreatePreRegistrationMappingsResponse)(nil), // 51: hero.orgs.v1.CreatePreRegistrationMappingsResponse
	(*GetPreRegistrationMappingRequest)(nil),      // 52: hero.orgs.v1.GetPreRegistrationMappingRequest
	(*GetPreRegistrationMappingResponse)(nil),     // 53: hero.orgs.v1.GetPreRegistrationMappingResponse
	(*ListPreRegistrationMappingsRequest)(nil),    // 54: hero.orgs.v1.ListPreRegistrationMappingsRequest
	(*ListPreRegistrationMappingsResponse)(nil),   // 55: hero.orgs.v1.ListPreRegistrationMappingsResponse
	(*UpdatePreRegistrationMappingRequest)(nil),   // 56: hero.orgs.v1.UpdatePreRegistrationMappingRequest
	(*UpdatePreRegistrationMappingResponse)(nil),  // 57: hero.orgs.v1.UpdatePreRegistrationMappingResponse
	(*DeletePreRegistrationMappingRequest)(nil),   // 58: hero.orgs.v1.DeletePreRegistrationMappingRequest
	(*DeletePreRegistrationMappingResponse)(nil),  // 59: hero.orgs.v1.DeletePreRegistrationMappingResponse
	(*MarkMappingAsUsedRequest)(nil),              // 60: hero.orgs.v1.MarkMappingAsUsedRequest
	(*MarkMappingAsUsedResponse)(nil),             // 61: hero.orgs.v1.MarkMappingAsUsedResponse
	(*timestamppb.Timestamp)(nil),                 // 62: google.protobuf.Timestamp
	(v2.AssetType)(0),                             // 63: hero.assets.v2.AssetType
}
var file_hero_orgs_v1_orgs_proto_depIdxs = []int32{
	20, // 0: hero.orgs.v1.GetOrgAPIUserPrivateByIdResponse.org_api_user:type_name -> hero.orgs.v1.OrgApiUserPrivate
	26, // 1: hero.orgs.v1.CreateOrgRequest.org:type_name -> hero.orgs.v1.Org
	26, // 2: hero.orgs.v1.CreateOrgResponse.org:type_name -> hero.orgs.v1.Org
	26, // 3: hero.orgs.v1.UpdateOrgRequest.org:type_name -> hero.orgs.v1.Org
	26, // 4: hero.orgs.v1.UpdateOrgResponse.org:type_name -> hero.orgs.v1.Org
	26, // 5: hero.orgs.v1.GetOrgResponse.org:type_name -> hero.orgs.v1.Org
	26, // 6: hero.orgs.v1.ListOrgsResponse.orgs:type_name -> hero.orgs.v1.Org
	19, // 7: hero.orgs.v1.ValidateOrgCredsResponse.org_api_user:type_name -> hero.orgs.v1.OrgApiUser
	62, // 8: hero.orgs.v1.OrgApiUser.created_at:type_name -> google.protobuf.Timestamp
	62, // 9: hero.orgs.v1.OrgApiUser.updated_at:type_name -> google.protobuf.Timestamp
	62, // 10: hero.orgs.v1.OrgApiUserPrivate.created_at:type_name -> google.protobuf.Timestamp
	62, // 11: hero.orgs.v1.OrgApiUserPrivate.updated_at:type_name -> google.protobuf.Timestamp
	62, // 12: hero.orgs.v1.OrgQueue.created_at:type_name -> google.protobuf.Timestamp
	62, // 13: hero.orgs.v1.OrgQueue.updated_at:type_name -> google.protobuf.Timestamp
	21, // 14: hero.orgs.v1.InsertOrgQueueResponse.org_queue:type_name -> hero.orgs.v1.OrgQueue
	0,  // 15: hero.orgs.v1.Org.service_type:type_name -> hero.orgs.v1.ServiceType
	62, // 16: hero.orgs.v1.Org.created_at:type_name -> google.protobuf.Timestamp
	62, // 17: hero.orgs.v1.Org.updated_at:type_name -> google.protobuf.Timestamp
	31, // 18: hero.orgs.v1.GetZelloChannelsResponse.zello_channels:type_name -> hero.orgs.v1.ZelloChannel
	62, // 19: hero.orgs.v1.ContactRecord.created_at:type_name -> google.protobuf.Timestamp
	62, // 20: hero.orgs.v1.ContactRecord.updated_at:type_name -> google.protobuf.Timestamp
	34, // 21: hero.orgs.v1.AddToContactBookResponse.contact:type_name -> hero.orgs.v1.ContactRecord
	34, // 22: hero.orgs.v1.UpdateContactInContactBookResponse.contact:type_name -> hero.orgs.v1.ContactRecord
	34, // 23: hero.orgs.v1.GetContactFromContactBookResponse.contact:type_name -> hero.orgs.v1.ContactRecord
	34, // 24: hero.orgs.v1.ListContactsInContactBookResponse.contacts:type_name -> hero.orgs.v1.ContactRecord
	34, // 25: hero.orgs.v1.GetContactByPhoneNumberResponse.contact:type_name -> hero.orgs.v1.ContactRecord
	63, // 26: hero.orgs.v1.PreRegistrationUserMapping.asset_type:type_name -> hero.assets.v2.AssetType
	62, // 27: hero.orgs.v1.PreRegistrationUserMapping.created_at:type_name -> google.protobuf.Timestamp
	62, // 28: hero.orgs.v1.PreRegistrationUserMapping.used_at:type_name -> google.protobuf.Timestamp
	63, // 29: hero.orgs.v1.CreatePreRegistrationMappingRequest.asset_type:type_name -> hero.assets.v2.AssetType
	47, // 30: hero.orgs.v1.CreatePreRegistrationMappingResponse.mapping:type_name -> hero.orgs.v1.PreRegistrationUserMapping
	48, // 31: hero.orgs.v1.CreatePreRegistrationMappingsRequest.mappings:type_name -> hero.orgs.v1.CreatePreRegistrationMappingRequest
	47, // 32: hero.orgs.v1.CreatePreRegistrationMappingsResponse.mappings:type_name -> hero.orgs.v1.PreRegistrationUserMapping
	47, // 33: hero.orgs.v1.GetPreRegistrationMappingResponse.mapping:type_name -> hero.orgs.v1.PreRegistrationUserMapping
	47, // 34: hero.orgs.v1.ListPreRegistrationMappingsResponse.mappings:type_name -> hero.orgs.v1.PreRegistrationUserMapping
	63, // 35: hero.orgs.v1.UpdatePreRegistrationMappingRequest.asset_type:type_name -> hero.assets.v2.AssetType
	47, // 36: hero.orgs.v1.UpdatePreRegistrationMappingResponse.mapping:type_name -> hero.orgs.v1.PreRegistrationUserMapping
	7,  // 37: hero.orgs.v1.OrgsService.CreateOrg:input_type -> hero.orgs.v1.CreateOrgRequest
	9,  // 38: hero.orgs.v1.OrgsService.UpdateOrg:input_type -> hero.orgs.v1.UpdateOrgRequest
	11, // 39: hero.orgs.v1.OrgsService.DeleteOrg:input_type -> hero.orgs.v1.DeleteOrgRequest
	13, // 40: hero.orgs.v1.OrgsService.GetOrg:input_type -> hero.orgs.v1.GetOrgRequest
	15, // 41: hero.orgs.v1.OrgsService.ListOrgs:input_type -> hero.orgs.v1.ListOrgsRequest
	17, // 42: hero.orgs.v1.OrgsService.ValidateOrgCreds:input_type -> hero.orgs.v1.ValidateOrgCredsRequest
	27, // 43: hero.orgs.v1.OrgsService.CreateOrgAPIUser:input_type -> hero.orgs.v1.CreateOrgAPIUserRequest
	5,  // 44: hero.orgs.v1.OrgsService.GetOrgAPIUserPrivateById:input_type -> hero.orgs.v1.GetOrgAPIUserPrivateByIdRequest
	29, // 45: hero.orgs.v1.OrgsService.GetZelloChannels:input_type -> hero.orgs.v1.GetZelloChannelsRequest
	22, // 46: hero.orgs.v1.OrgsService.InsertOrgQueue:input_type -> hero.orgs.v1.InsertOrgQueueRequest
	24, // 47: hero.orgs.v1.OrgsService.CreateOrgTwilioQueue:input_type -> hero.orgs.v1.CreateOrgTwilioQueueRequest
	32, // 48: hero.orgs.v1.OrgsService.CreateCognitoUser:input_type -> hero.orgs.v1.CreateCognitoUserRequest
	35, // 49: hero.orgs.v1.OrgsService.AddToContactBook:input_type -> hero.orgs.v1.AddToContactBookRequest
	37, // 50: hero.orgs.v1.OrgsService.UpdateContactInContactBook:input_type -> hero.orgs.v1.UpdateContactInContactBookRequest
	39, // 51: hero.orgs.v1.OrgsService.DeleteFromContactBook:input_type -> hero.orgs.v1.DeleteFromContactBookRequest
	41, // 52: hero.orgs.v1.OrgsService.GetContactFromContactBook:input_type -> hero.orgs.v1.GetContactFromContactBookRequest
	43, // 53: hero.orgs.v1.OrgsService.ListContactsInContactBook:input_type -> hero.orgs.v1.ListContactsInContactBookRequest
	45, // 54: hero.orgs.v1.OrgsService.GetContactByPhoneNumber:input_type -> hero.orgs.v1.GetContactByPhoneNumberRequest
	1,  // 55: hero.orgs.v1.OrgsService.TurnOnGuestMode:input_type -> hero.orgs.v1.TurnOnGuestModeRequest
	3,  // 56: hero.orgs.v1.OrgsService.TurnOffGuestMode:input_type -> hero.orgs.v1.TurnOffGuestModeRequest
	48, // 57: hero.orgs.v1.OrgsService.CreatePreRegistrationMapping:input_type -> hero.orgs.v1.CreatePreRegistrationMappingRequest
	50, // 58: hero.orgs.v1.OrgsService.CreatePreRegistrationMappings:input_type -> hero.orgs.v1.CreatePreRegistrationMappingsRequest
	52, // 59: hero.orgs.v1.OrgsService.GetPreRegistrationMapping:input_type -> hero.orgs.v1.GetPreRegistrationMappingRequest
	54, // 60: hero.orgs.v1.OrgsService.ListPreRegistrationMappings:input_type -> hero.orgs.v1.ListPreRegistrationMappingsRequest
	56, // 61: hero.orgs.v1.OrgsService.UpdatePreRegistrationMapping:input_type -> hero.orgs.v1.UpdatePreRegistrationMappingRequest
	58, // 62: hero.orgs.v1.OrgsService.DeletePreRegistrationMapping:input_type -> hero.orgs.v1.DeletePreRegistrationMappingRequest
	60, // 63: hero.orgs.v1.OrgsService.MarkMappingAsUsed:input_type -> hero.orgs.v1.MarkMappingAsUsedRequest
	8,  // 64: hero.orgs.v1.OrgsService.CreateOrg:output_type -> hero.orgs.v1.CreateOrgResponse
	10, // 65: hero.orgs.v1.OrgsService.UpdateOrg:output_type -> hero.orgs.v1.UpdateOrgResponse
	12, // 66: hero.orgs.v1.OrgsService.DeleteOrg:output_type -> hero.orgs.v1.DeleteOrgResponse
	14, // 67: hero.orgs.v1.OrgsService.GetOrg:output_type -> hero.orgs.v1.GetOrgResponse
	16, // 68: hero.orgs.v1.OrgsService.ListOrgs:output_type -> hero.orgs.v1.ListOrgsResponse
	18, // 69: hero.orgs.v1.OrgsService.ValidateOrgCreds:output_type -> hero.orgs.v1.ValidateOrgCredsResponse
	28, // 70: hero.orgs.v1.OrgsService.CreateOrgAPIUser:output_type -> hero.orgs.v1.CreateOrgAPIUserResponse
	6,  // 71: hero.orgs.v1.OrgsService.GetOrgAPIUserPrivateById:output_type -> hero.orgs.v1.GetOrgAPIUserPrivateByIdResponse
	30, // 72: hero.orgs.v1.OrgsService.GetZelloChannels:output_type -> hero.orgs.v1.GetZelloChannelsResponse
	23, // 73: hero.orgs.v1.OrgsService.InsertOrgQueue:output_type -> hero.orgs.v1.InsertOrgQueueResponse
	25, // 74: hero.orgs.v1.OrgsService.CreateOrgTwilioQueue:output_type -> hero.orgs.v1.CreateOrgTwilioQueueResponse
	33, // 75: hero.orgs.v1.OrgsService.CreateCognitoUser:output_type -> hero.orgs.v1.CreateCognitoUserResponse
	36, // 76: hero.orgs.v1.OrgsService.AddToContactBook:output_type -> hero.orgs.v1.AddToContactBookResponse
	38, // 77: hero.orgs.v1.OrgsService.UpdateContactInContactBook:output_type -> hero.orgs.v1.UpdateContactInContactBookResponse
	40, // 78: hero.orgs.v1.OrgsService.DeleteFromContactBook:output_type -> hero.orgs.v1.DeleteFromContactBookResponse
	42, // 79: hero.orgs.v1.OrgsService.GetContactFromContactBook:output_type -> hero.orgs.v1.GetContactFromContactBookResponse
	44, // 80: hero.orgs.v1.OrgsService.ListContactsInContactBook:output_type -> hero.orgs.v1.ListContactsInContactBookResponse
	46, // 81: hero.orgs.v1.OrgsService.GetContactByPhoneNumber:output_type -> hero.orgs.v1.GetContactByPhoneNumberResponse
	2,  // 82: hero.orgs.v1.OrgsService.TurnOnGuestMode:output_type -> hero.orgs.v1.TurnOnGuestModeResponse
	4,  // 83: hero.orgs.v1.OrgsService.TurnOffGuestMode:output_type -> hero.orgs.v1.TurnOffGuestModeResponse
	49, // 84: hero.orgs.v1.OrgsService.CreatePreRegistrationMapping:output_type -> hero.orgs.v1.CreatePreRegistrationMappingResponse
	51, // 85: hero.orgs.v1.OrgsService.CreatePreRegistrationMappings:output_type -> hero.orgs.v1.CreatePreRegistrationMappingsResponse
	53, // 86: hero.orgs.v1.OrgsService.GetPreRegistrationMapping:output_type -> hero.orgs.v1.GetPreRegistrationMappingResponse
	55, // 87: hero.orgs.v1.OrgsService.ListPreRegistrationMappings:output_type -> hero.orgs.v1.ListPreRegistrationMappingsResponse
	57, // 88: hero.orgs.v1.OrgsService.UpdatePreRegistrationMapping:output_type -> hero.orgs.v1.UpdatePreRegistrationMappingResponse
	59, // 89: hero.orgs.v1.OrgsService.DeletePreRegistrationMapping:output_type -> hero.orgs.v1.DeletePreRegistrationMappingResponse
	61, // 90: hero.orgs.v1.OrgsService.MarkMappingAsUsed:output_type -> hero.orgs.v1.MarkMappingAsUsedResponse
	64, // [64:91] is the sub-list for method output_type
	37, // [37:64] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_hero_orgs_v1_orgs_proto_init() }
func file_hero_orgs_v1_orgs_proto_init() {
	if File_hero_orgs_v1_orgs_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hero_orgs_v1_orgs_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   61,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hero_orgs_v1_orgs_proto_goTypes,
		DependencyIndexes: file_hero_orgs_v1_orgs_proto_depIdxs,
		EnumInfos:         file_hero_orgs_v1_orgs_proto_enumTypes,
		MessageInfos:      file_hero_orgs_v1_orgs_proto_msgTypes,
	}.Build()
	File_hero_orgs_v1_orgs_proto = out.File
	file_hero_orgs_v1_orgs_proto_rawDesc = nil
	file_hero_orgs_v1_orgs_proto_goTypes = nil
	file_hero_orgs_v1_orgs_proto_depIdxs = nil
}
