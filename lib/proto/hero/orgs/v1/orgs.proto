syntax = "proto3";

package hero.orgs.v1;

option go_package = "proto/hero/orgs/v1;orgs";

import "google/protobuf/timestamp.proto";
import "hero/permissions/v1/permissions.proto";
import "hero/assets/v2/assets.proto";

service OrgsService {
    rpc CreateOrg(CreateOrgRequest) returns (CreateOrgResponse) {}
    rpc UpdateOrg(UpdateOrgRequest) returns (UpdateOrgResponse) {}
    rpc DeleteOrg(DeleteOrgRequest) returns (DeleteOrgResponse) {}
    rpc GetOrg(GetOrgRequest) returns (GetOrgResponse) {}
    rpc ListOrgs(ListOrgsRequest) returns (ListOrgsResponse) {}

    rpc ValidateOrgCreds(ValidateOrgCredsRequest) returns (ValidateOrgCredsResponse) {}
    rpc CreateOrgAPIUser(CreateOrgAPIUserRequest) returns (CreateOrgAPIUserResponse) {}
    rpc GetOrgAPIUserPrivateById(GetOrgAPIUserPrivateByIdRequest) returns (GetOrgAPIUserPrivateByIdResponse) {}
    rpc GetZelloChannels(GetZelloChannelsRequest) returns (GetZelloChannelsResponse) {}
    rpc InsertOrgQueue(InsertOrgQueueRequest) returns (InsertOrgQueueResponse) {}
    rpc CreateOrgTwilioQueue(CreateOrgTwilioQueueRequest) returns (CreateOrgTwilioQueueResponse) {}
    rpc CreateCognitoUser(CreateCognitoUserRequest) returns (CreateCognitoUserResponse) {}

    // Organization Contact Book Management
    rpc AddToContactBook(AddToContactBookRequest) returns (AddToContactBookResponse) {}
    rpc UpdateContactInContactBook(UpdateContactInContactBookRequest) returns (UpdateContactInContactBookResponse) {}
    rpc DeleteFromContactBook(DeleteFromContactBookRequest) returns (DeleteFromContactBookResponse) {}
    rpc GetContactFromContactBook(GetContactFromContactBookRequest) returns (GetContactFromContactBookResponse) {}
    rpc ListContactsInContactBook(ListContactsInContactBookRequest) returns (ListContactsInContactBookResponse) {}
    rpc GetContactByPhoneNumber(GetContactByPhoneNumberRequest) returns (GetContactByPhoneNumberResponse) {}

    rpc TurnOnGuestMode(TurnOnGuestModeRequest) returns (TurnOnGuestModeResponse) {}
    rpc TurnOffGuestMode(TurnOffGuestModeRequest) returns (TurnOffGuestModeResponse) {
        // allow any user to turn off guest mode
        // this ensures that the user can always get out of guest mode, regardless
        // of the permissions of the guest role
        // otherwise, we'd have to one-off tell permissions check to look at
        // the primary org, just for this one rpc
        // But leaving guest mode is no security risk, so we can just open it up
        option (hero.permissions.v1.access_control) = { required_protection_level: OPEN };
    }

    // Pre-registration user mapping management
    rpc CreatePreRegistrationMapping(CreatePreRegistrationMappingRequest) returns (CreatePreRegistrationMappingResponse) {}
    rpc CreatePreRegistrationMappings(CreatePreRegistrationMappingsRequest) returns (CreatePreRegistrationMappingsResponse) {}
    rpc GetPreRegistrationMapping(GetPreRegistrationMappingRequest) returns (GetPreRegistrationMappingResponse) {}
    rpc ListPreRegistrationMappings(ListPreRegistrationMappingsRequest) returns (ListPreRegistrationMappingsResponse) {}
    rpc UpdatePreRegistrationMapping(UpdatePreRegistrationMappingRequest) returns (UpdatePreRegistrationMappingResponse) {}
    rpc DeletePreRegistrationMapping(DeletePreRegistrationMappingRequest) returns (DeletePreRegistrationMappingResponse) {}
    rpc MarkMappingAsUsed(MarkMappingAsUsedRequest) returns (MarkMappingAsUsedResponse) {}
}

message TurnOnGuestModeRequest {
    int32 org_id = 1;
}

message TurnOnGuestModeResponse {
    bool success = 1;
}

message TurnOffGuestModeRequest {
    int32 org_id = 1;
}
message TurnOffGuestModeResponse {
    bool success = 1;
}

message GetOrgAPIUserPrivateByIdRequest {
    string user_id = 1;
}

message GetOrgAPIUserPrivateByIdResponse {
    OrgApiUserPrivate org_api_user = 1;
}

message CreateOrgRequest {
    Org org = 1;
}

message CreateOrgResponse {
    Org org = 1;
}

message UpdateOrgRequest {
    Org org = 1;
}
message UpdateOrgResponse {
    Org org = 1;
}

message DeleteOrgRequest {
    int32 id = 1;
}

message DeleteOrgResponse {}

message GetOrgRequest {
    int32 id = 1;
}

message GetOrgResponse {
    Org org = 1;
}

message ListOrgsRequest {}

message ListOrgsResponse {
    repeated Org orgs = 1;
}

message ValidateOrgCredsRequest {
    string username = 1;
    string password = 2;
}

message ValidateOrgCredsResponse {
    bool valid = 1;
    OrgApiUser org_api_user = 2;
}

message OrgApiUser {
    string id = 1;
    int32 org_id = 2;
    string username = 3;
    string encrypted_password = 4;
    string hashed_password = 5;
    google.protobuf.Timestamp created_at = 6;
    google.protobuf.Timestamp updated_at = 7;
}

message OrgApiUserPrivate {
    string id = 1;
    int32 org_id = 2;
    string username = 3;
    string encrypted_password = 4;
    string hashed_password = 5;
    string raw_password = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
}

message OrgQueue {
    int32 id = 1;
    string friendly_name = 2;
    string twilio_queue_sid = 3;
    string description = 4;
    google.protobuf.Timestamp created_at = 5;
    google.protobuf.Timestamp updated_at = 6;
    int32 org_id = 7;
}

message InsertOrgQueueRequest {
    string friendly_name = 1;
    string twilio_queue_sid = 2;
    string description = 3;
    int32 org_id = 4;
}

message InsertOrgQueueResponse {
    OrgQueue org_queue = 1;
}

message CreateOrgTwilioQueueRequest {
    int32 org_id = 1;
    string friendly_name = 2;
}

message CreateOrgTwilioQueueResponse {
    string queue_sid = 1;
}

message Org {
    int32 id = 1;
    string name = 2;
    repeated string domains = 3;
    string twiml_app_sid = 4;
    string twilio_number = 5;
    string twilio_number_sid = 6;
    ServiceType service_type = 7;
    google.protobuf.Timestamp created_at = 8;
    google.protobuf.Timestamp updated_at = 9;
    string template_id = 10;  // ID of the template used to setup the org's features
    
    // Call Forwarding Configuration
    bool is_call_forwarding_enabled = 11;  // Whether call forwarding is enabled for this organization
    string primary_phone_number = 12;      // The primary phone number for the organization (used for Twilio setup and call forwarding)
    string call_forwarding_type = 13;      // Type of call forwarding: "PSTN" (standard phone) or "SIP" (VoIP)
    string sip_uri = 14;                   // SIP URI for SIP forwarding (format: sip:<EMAIL>)
}

enum ServiceType {
    SERVICE_TYPE_UNSPECIFIED = 0;
    SERVICE_TYPE_DEMO = 1;
    SERVICE_TYPE_PRODUCTION = 2;
}

message CreateOrgAPIUserRequest {
    int32 org_id = 1;
}

message CreateOrgAPIUserResponse {
    string username = 1;
    string encrypted_password = 2;
    string hashed_password = 3;
}

message GetZelloChannelsRequest {
}

message GetZelloChannelsResponse {
    repeated ZelloChannel zello_channels = 1;
}

message ZelloChannel {
    string id = 1;
    int32 org_id = 2;
    string zello_channel_id = 3;
    string display_name = 4;
}

message CreateCognitoUserRequest {
    int32 org_id = 1;
    string username = 2;
    string email = 3;
    string password = 4;
}

message CreateCognitoUserResponse {
    bool success = 1;
    string cognito_sub_id = 2;  // The Cognito user ID (sub)
}

// Organization Contact Book Messages
message ContactRecord {
    string id = 1;
    int32 org_id = 2;
    string name = 3;
    string phone = 4;
    google.protobuf.Timestamp created_at = 5;
    google.protobuf.Timestamp updated_at = 6;
}

message AddToContactBookRequest {
    int32 org_id = 1;
    string name = 2;
    string phone = 3;
}

message AddToContactBookResponse {
    ContactRecord contact = 1;
}

message UpdateContactInContactBookRequest {
    string id = 1;
    string name = 2;
    string phone = 3;
}

message UpdateContactInContactBookResponse {
    ContactRecord contact = 1;
}

message DeleteFromContactBookRequest {
    string id = 1;
}

message DeleteFromContactBookResponse {}

message GetContactFromContactBookRequest {
    string id = 1;
}

message GetContactFromContactBookResponse {
    ContactRecord contact = 1;
}

message ListContactsInContactBookRequest {
    int32 org_id = 1;
    string page_token = 2;  // Token for pagination (empty for first page)
    int32 page_size = 3;    // Number of contacts to return (max 100)
}

message ListContactsInContactBookResponse {
    repeated ContactRecord contacts = 1;
    string next_page_token = 2;  // Token for next page (empty if no more pages)
    int32 total_count = 3;       // Total number of contacts in the contact book
}

message GetContactByPhoneNumberRequest {
    int32 org_id = 1;
    string phone = 2;
}

message GetContactByPhoneNumberResponse {
    ContactRecord contact = 1;
}

// Pre-registration user mapping messages
message PreRegistrationUserMapping {
    string id = 1;
    string email = 2;
    int32 org_id = 3;
    string role_name = 4;
    hero.assets.v2.AssetType asset_type = 5;
    google.protobuf.Timestamp created_at = 6;
    google.protobuf.Timestamp used_at = 7;
    string created_by = 8;
}

message CreatePreRegistrationMappingRequest {
    string email = 1;
    int32 org_id = 2;
    string role_name = 3;
    hero.assets.v2.AssetType asset_type = 4; // Optional, defaults to RESPONDER
    string created_by = 5;
}

message CreatePreRegistrationMappingResponse {
    PreRegistrationUserMapping mapping = 1;
}

message CreatePreRegistrationMappingsRequest {
    repeated CreatePreRegistrationMappingRequest mappings = 1;
}

message CreatePreRegistrationMappingsResponse {
    repeated PreRegistrationUserMapping mappings = 1;
    repeated string errors = 2; // Error messages for failed mappings
}

message GetPreRegistrationMappingRequest {
    string email = 1;
    int32 org_id = 2;
}

message GetPreRegistrationMappingResponse {
    PreRegistrationUserMapping mapping = 1;
}

message ListPreRegistrationMappingsRequest {
    int32 org_id = 1;
    string page_token = 2;
    int32 page_size = 3;
    bool include_used = 4; // Whether to include already used mappings
}

message ListPreRegistrationMappingsResponse {
    repeated PreRegistrationUserMapping mappings = 1;
    string next_page_token = 2;
    int32 total_count = 3;
}

message UpdatePreRegistrationMappingRequest {
    string id = 1;
    string role_name = 2;
    hero.assets.v2.AssetType asset_type = 3;
}

message UpdatePreRegistrationMappingResponse {
    PreRegistrationUserMapping mapping = 1;
}

message DeletePreRegistrationMappingRequest {
    string id = 1;
}

message DeletePreRegistrationMappingResponse {}

message MarkMappingAsUsedRequest {
    string mapping_id = 1;
}

message MarkMappingAsUsedResponse {
    bool success = 1;
}